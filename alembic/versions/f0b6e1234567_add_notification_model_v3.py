"""Add notification model v3

Revision ID: f0b6e1234567
Revises: 9250b104347a
Create Date: 2025-01-18 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f0b6e1234567'
down_revision: Union[str, None] = '9250b104347a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # This migration has been modified to only add additional indexes
    # The notifications table was already created by db2e33e19217
    connection = op.get_bind()

    # Check if notifications table exists and add any missing indexes
    result = connection.execute(
        sa.text("""
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name = 'notifications'
        )
    """)
    ).scalar()

    if not result:
        # If table doesn't exist, this should have been created by previous migration
        # Log the issue but don't fail
        print("WARNING: notifications table not found. Please ensure db2e33e19217 migration ran successfully.")

    # Add any additional indexes that might be missing
    try:
        op.create_index('ix_notifications_type', 'notifications', ['type'], unique=False)
    except:
        print("ix_notifications_type index already exists or table doesn't exist")

    try:
        op.create_index('ix_notifications_user_id', 'notifications', ['user_id'], unique=False)
    except:
        print("ix_notifications_user_id index already exists or table doesn't exist")

    # Add composite index if not exists
    try:
        op.create_index('ix_notifications_user_status', 'notifications', ['user_id', 'status'], unique=False)
    except:
        print("ix_notifications_user_status index already exists or table doesn't exist")


def downgrade() -> None:
    # Modified to only drop indexes added by this migration
    # The main notifications table will be dropped by db2e33e19217 migration
    try:
        op.drop_index('ix_notifications_type', table_name='notifications')
    except:
        print("ix_notifications_type index doesn't exist (already dropped or table doesn't exist)")

    try:
        op.drop_index('ix_notifications_user_id', table_name='notifications')
    except:
        print("ix_notifications_user_id index doesn't exist (already dropped or table doesn't exist)")

    try:
        op.drop_index('ix_notifications_user_status', table_name='notifications')
    except:
        print("ix_notifications_user_status index doesn't exist (already dropped or table doesn't exist)")