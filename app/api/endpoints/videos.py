from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.core.limiter import limiter
from app.core.logging import logger
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse, CursorPaginator
from app.core.permission_system import (
    Action,
    ResourceType,
    Scope,
)
from app.schemas.video import VideoOut
from app.services.behavior_tracking_service import BehaviorTrackingService
from app.services.recommendation_service import RecommendationService
from app.services.service_factory import (
    get_behavior_tracking_service,
    get_recommendation_service,
    get_video_aggregation_service,
    get_video_folder_service,
)
from app.services.video_aggregation_service import VideoAggregationService
from app.services.video_folder_service import VideoFolderService

router = APIRouter()


@router.get("/recommendations", response_model=CursorPaginationResponse[VideoOut])
async def get_video_recommendations(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取视频推荐列表。

    - **权限**: 登录用户。
    - **逻辑**: 调用推荐服务获取分页后的视频推荐。
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="需要登录才能获取个性化推荐"
        )

    # 1. 从推荐服务获取分页的、特定内容类型的推荐项
    recommendation_items = await recommendation_service.get_paginated_content_recommendations(
        user_id=current_user.id,
        content_type="video",
        page=pagination.page or 1,
        limit=pagination.size,
    )

    if not recommendation_items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    # 2. 提取视频ID
    video_ids = [item.content_id for item in recommendation_items]

    # 3. 使用聚合服务获取完整的视频信息
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db,
        video_ids=video_ids,
        current_user=current_user,
    )

    # 保持推荐顺序
    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[vid] for vid in video_ids if vid in videos_map]

    # 4. 构建并返回最终的分页响应 (简化版，因为服务层不支持游标)
    return CursorPaginationResponse(
        items=sorted_videos,
        total_count=len(sorted_videos),
        has_next=len(sorted_videos) == pagination.size,
        has_previous=(pagination.page or 1) > 1,
    )


@router.get("/{video_id}/similar", response_model=CursorPaginationResponse[VideoOut])
async def get_similar_videos(
    video_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取相似视频列表。

    - **权限**: 公开。
    - **逻辑**: 调用推荐服务获取相似内容。
    """
    # 1. 检查源视频是否存在
    source_video = await crud.video.get(db=db, id=video_id)
    if not source_video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="源视频不存在")

    # 2. 从推荐服务获取相似内容
    similar_items = await recommendation_service.get_similar_content(
        content_type="video", content_id=video_id, limit=pagination.size
    )

    if not similar_items:
        return CursorPaginationResponse(
            items=[],
            total_count=0,
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
        )

    # 3. 提取视频ID
    video_ids = [item.content_id for item in similar_items]

    # 4. 使用聚合服务获取完整的视频信息
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db,
        video_ids=video_ids,
        current_user=current_user,
    )

    # 保持推荐顺序
    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[vid] for vid in video_ids if vid in videos_map]

    # 5. 构建并返回响应 (相似内容不分页)
    return CursorPaginationResponse(
        items=sorted_videos,
        total_count=len(sorted_videos),
        has_next=False,
        has_previous=False,
        next_cursor=None,
        previous_cursor=None,
    )


@router.get("/hot", response_model=CursorPaginationResponse[VideoOut])
async def get_hot_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取热门视频列表（分页）
    """
    # 1. 将游标转换为偏移量
    try:
        offset = int(pagination.cursor) if pagination.cursor else 0
    except (ValueError, TypeError):
        offset = 0

    # 2. 从服务层获取分页后的热门内容ID
    paginated_result = await recommendation_service.get_hot_content(
        content_type="video", limit=pagination.size, offset=offset
    )

    if not paginated_result.items:
        return CursorPaginationResponse(
            items=[],
            has_next=False,
            has_previous=offset > 0,
            next_cursor=None,
            previous_cursor=str(offset - pagination.size) if offset > 0 else None,
        )

    # 3. 聚合数据
    video_ids = [item.content_id for item in paginated_result.items]
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db,
        video_ids=video_ids,
        current_user=current_user,
    )

    # 保持分页顺序
    videos_map = {video.id: video for video in aggregated_videos}
    sorted_videos = [videos_map[video_id] for video_id in video_ids if video_id in videos_map]

    # 4. 构建并返回最终的分页响应
    next_cursor = str(offset + pagination.size) if paginated_result.has_next else None
    previous_cursor = str(offset - pagination.size) if offset > 0 else None

    return CursorPaginationResponse(
        items=sorted_videos,
        has_next=paginated_result.has_next,
        has_previous=offset > 0,
        next_cursor=next_cursor,
        previous_cursor=previous_cursor,
    )


@router.get(
    "/category/{category_id}",
    response_model=CursorPaginationResponse[VideoOut],
    summary="获取指定分类下的视频列表",
)
async def get_category_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    category_id: int = Path(..., description="分类ID"),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取指定分类下的视频列表，并进行分页。

    - **权限**: 公开接口，游客和登录用户均可访问。
    - **过滤**: 仅返回已发布、已审核且未删除的视频。
    - **分页**: 支持高效的游标分页。
    """
    # 检查分类是否存在
    category = await crud.category.get(db=db, id=category_id)
    if not category:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="分类不存在")

    # 1. 从 CRUD 层获取分页后的视频ID
    paginated_ids_response = await crud.video.get_paginated_video_ids(
        db=db,
        params=pagination,
        filters={"category_id": category_id},
        access_level="public",
    )

    video_ids = paginated_ids_response.items
    if not video_ids:
        return CursorPaginationResponse(items=[], **paginated_ids_response.dict(exclude={"items"}))

    # 2. 使用聚合服务获取完整的视频信息
    aggregated_videos = await video_aggregation_service.get_aggregated_videos_by_ids(
        db=db,
        video_ids=video_ids,
        current_user=current_user,
    )

    # 3. 构建并返回最终的分页响应
    return CursorPaginationResponse(
        items=aggregated_videos,
        total_count=paginated_ids_response.total_count,
        has_next=paginated_ids_response.has_next,
        has_previous=paginated_ids_response.has_previous,
        next_cursor=paginated_ids_response.next_cursor,
        previous_cursor=paginated_ids_response.previous_cursor,
    )


@router.get(
    "/users/{user_id}/folders",
    response_model=CursorPaginationResponse[schemas.VideoFolder],
    summary="获取指定用户的文件夹列表",
)
async def get_user_folders(
    user_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    include_video_count: bool = Query(True, description="是否包含视频数量统计"),
    include_children: bool = Query(False, description="是否包含子文件夹信息"),
    include_recent_videos: bool = Query(
        False, description="是否包含每个文件夹的最新视频预览（最多3个）"
    ),
    include_default_folder: bool = Query(True, description="是否包含默认文件夹"),
    include_total: bool = Query(False, description="是否包含总数（影响性能）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """
    获取指定用户的文件夹列表

    **权限控制**：
    - **用户本人**：可以访问所有文件夹（包括私有文件夹）
    - **其他用户**：只能访问公开文件夹
    - **未登录用户**：只能访问公开文件夹

    **功能特性**：
    - 支持游标分页，提高大量数据加载性能
    - 可选择是否包含视频数量统计
    - 可选择是否包含子文件夹信息
    - 可选择是否包含最新视频预览
    - 可选择是否包含默认文件夹
    - 自动权限过滤

    **分页参数**：
    - **cursor**: 游标位置，用于获取下一页数据
    - **size**: 每页大小，范围1-100
    - **order_by**: 排序字段，默认为id
    - **order_direction**: 排序方向，asc或desc
    """
    # 1. 检查目标用户是否存在
    target_user = await crud.user.get(db=db, id=user_id)
    if not target_user:
        logger.error(f"[文件夹调试] 目标用户不存在 - user_id: {user_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 2. 权限判断
    is_own_folders = current_user and current_user.id == user_id
    logger.info(f"[文件夹调试] 权限判断 - is_own_folders: {is_own_folders}")

    # 权限检查：如果是访问他人的文件夹，需要有相应权限或只能看公开的
    if not is_own_folders:
        from app.core.permission_system import Permission, PermissionChecker

        # 检查是否有查看所有文件夹的权限
        has_view_all_permission = await PermissionChecker.check_permission(
            db,
            current_user,
            Permission(resource=ResourceType.FOLDER, action=Action.READ, scope=Scope.ALL),
        )
        # 如果没有管理权限，只能查看公开文件夹（后续会过滤）
        if not has_view_all_permission:
            user_info = current_user.id if current_user else "anonymous"
            logger.info(f"[文件夹调试] 用户 {user_info} 访问用户 {user_id} 的公开文件夹")
        logger.info(
            f"[文件夹调试] 权限检查完成 - has_view_all_permission: {has_view_all_permission}"
        )

    # 3. 构建查询获取用户的文件夹列表
    query = select(crud.video_folder.model).where(
        crud.video_folder.model.user_id == user_id,
        crud.video_folder.model.is_deleted.is_(False),
    )

    # 是否包含默认文件夹的筛选
    if not include_default_folder:
        query = query.where(crud.video_folder.model.is_default.is_(False))

    # 4. 权限过滤：非本人只能看到公开文件夹
    if not is_own_folders:
        # 修正查询条件 - 使用access_level而不是is_public属性
        query = query.where(crud.video_folder.model.access_level >= 1)
    paginated_result = await CursorPaginator.paginate_query(
        db=db,
        query=query,
        params=pagination,
        cursor_field=pagination.order_by,
        include_total=include_total,
    )
    folders = paginated_result.items
    # 6. 补充文件夹信息
    for folder in folders:
        if include_video_count:
            # 获取文件夹中的视频数量
            from sqlalchemy import func

            video_count = (
                await db.execute(
                    select(func.count())
                    .select_from(crud.video.model)
                    .where(
                        crud.video.model.folder_id == folder.id,
                        crud.video.model.is_deleted.is_(False),
                    )
                )
            ).scalar()
            # 使用update_video_count方法更新视频数量，而不是直接设置属性
            folder.update_video_count(video_count)

        if include_children:
            # 检查是否有子文件夹（通过parent_id）
            children_query = select(crud.video_folder.model).where(
                crud.video_folder.model.parent_id == folder.id,
                crud.video_folder.model.is_deleted.is_(False),
            )

            # 如果不是本人，也要过滤子文件夹的公开性
            if not is_own_folders:
                children_query = children_query.where(crud.video_folder.model.is_public.is_(True))

            result = await db.execute(children_query)
            children = result.scalars().all()
            folder.has_children = bool(children)

        if include_recent_videos:
            # 获取文件夹中最新的3个视频作为预览
            video_query = select(crud.video.model).where(
                crud.video.model.folder_id == folder.id,
                crud.video.model.is_deleted.is_(False),
            )

            # 根据权限过滤视频
            if not is_own_folders:
                video_query = video_query.where(
                    crud.video.model.is_published.is_(True),
                    crud.video.model.is_approved.is_(True),
                )

            video_query = video_query.order_by(crud.video.model.created_at.desc()).limit(3)

            result = await db.execute(video_query)
            recent_videos = result.scalars().all()

            # 将视频信息添加到文件夹对象（需要扩展 schema）
            folder.recent_videos = [
                {
                    "id": video.id,
                    "title": video.title,
                    "cover_url": video.cover_url,
                    "duration": video.duration,
                    "created_at": video.created_at,
                }
                for video in recent_videos
            ]

    # 7. 构造新的分页响应并返回
    response = CursorPaginationResponse(
        items=folders,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )

    return response


@router.get(
    "/folders/{folder_id}/videos",
    response_model=CursorPaginationResponse[VideoOut],
    summary="获取指定文件夹下的视频列表 (重构版)",
)
async def get_folder_videos(
    folder_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    cursor: int = Query(0, description="游标位置，用于分页"),
    size: int = Query(20, description="每页大小", ge=1, le=100),
    status: str | None = Query(
        None,
        alias="status",
        description="视频状态筛选 (draft, published_pending, published_rejected, published_approved)",
    ),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取指定文件夹下的视频列表

    **权限控制**:
    - **文件夹所有者**: 可以访问所有状态的视频。
    - **其他用户**: 只能访问公开文件夹中已发布且已审核通过的视频。

    **功能特性**:
    - 使用 VideoAggregationService 进行数据聚合
    - 支持游标分页
    - 支持状态过滤
    - 自动权限过滤和数据聚合
    """
    # 1. 检查文件夹是否存在
    folder = await crud.video_folder.get(db=db, id=folder_id)
    if not folder:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文件夹不存在")

    # 2. 权限检查
    is_own_folder = current_user and current_user.id == folder.user_id
    is_admin = current_user and current_user.is_superuser
    can_view_all = is_own_folder or is_admin

    # 如果不是文件夹所有者，检查文件夹是否公开
    if not can_view_all and not folder.is_public:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权访问此文件夹")

    # 检查 status 参数的使用权限
    if status and not can_view_all:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="没有权限查看草稿或待审核视频"
        )

    # 3. 调用 VideoAggregationService 获取视频列表
    result = await video_aggregation_service.get_videos_by_folder(
        db=db,
        folder_id=folder_id,
        cursor=cursor,
        size=size,
        status=status,
        current_user=current_user,
    )

    return result


@router.get("/{video_id}", response_model=VideoOut)
@limiter.limit("100/minute")
async def read_video(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    video_aggregation_service: VideoAggregationService = Depends(get_video_aggregation_service),
) -> Any:
    """
    获取单个视频的完整聚合信息 (重构版)
    - 返回 VideoOut 模型，包含作者、统计等所有信息
    - 权限控制
    """
    # 1. 权限检查并获取视频对象
    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在或无权访问")

    # 2. 调用聚合服务获取完整视频信息
    video_out = await video_aggregation_service.get_aggregated_video(
        db=db, video_id=video_id, current_user=current_user
    )

    if not video_out:
        # 理论上不应发生，因为我们已经通过了权限检查
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="无法获取视频聚合信息")

    return video_out


@router.put("/{video_id}", response_model=schemas.VideoOut)
async def update_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    video_in: schemas.VideoUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
) -> Any:
    """更新视频（使用编排服务）"""
    video = await video_folder_service.update_video(
        db=db, video_id=video_id, obj_in=video_in, current_user=current_user
    )
    return video


@router.delete("/{video_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
    video_folder_service: VideoFolderService = Depends(get_video_folder_service),
) -> None:
    """删除视频（使用编排服务）"""
    await video_folder_service.delete_video(db=db, video_id=video_id, current_user=current_user)


@router.post("/{video_id}/progress", response_model=dict)
async def update_video_progress(
    video_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    progress_seconds: int = Query(..., description="播放进度（秒）"),
    total_duration: int = Query(..., description="视频总时长（秒）"),
    current_user: models.User = Depends(deps.get_current_active_user),
    behavior_tracking_service: BehaviorTrackingService = Depends(get_behavior_tracking_service),
) -> Any:
    """更新视频播放进度

    **功能说明**：
    - 记录用户的视频播放进度
    - 支持断点续播功能
    - 自动计算播放完成率
    """
    # 检查视频是否存在
    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在")

    # 计算播放完成率
    completion_rate = min(progress_seconds / total_duration, 1.0) if total_duration > 0 else 0

    # 记录播放进度（这里需要创建 VideoProgress 模型）
    # 播放进度追踪使用 behavior_tracking_service 记录详细元数据
    await behavior_tracking_service.track_interaction(
        db=db,
        user_id=current_user.id,
        content_type="video",
        content_id=video_id,
        interaction_type="watch_progress",
        extra_data={
            "progress_seconds": progress_seconds,
            "total_duration": total_duration,
            "completion_rate": completion_rate,
        },
    )

    return {
        "message": "播放进度已更新",
        "progress_seconds": progress_seconds,
        "completion_rate": completion_rate,
        "is_completed": completion_rate >= 0.9,  # 90% 以上算完成
    }


@router.get("/{video_id}/progress", response_model=dict)
async def get_video_progress(
    video_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取视频播放进度"""
    # 检查视频是否存在
    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在")

    # 查询最近的播放进度记录
    from sqlalchemy import desc

    from app.models.history import History

    result = await db.execute(
        select(History)
        .where(
            History.user_id == current_user.id,
            History.content_type == "video",
            History.content_id == video_id,
            History.action == "watch_progress",
        )
        .order_by(desc(History.created_at))
        .limit(1)
    )

    latest_progress = result.scalar_one_or_none()

    if not latest_progress:
        return {
            "progress_seconds": 0,
            "completion_rate": 0.0,
            "is_completed": False,
            "last_watched": None,
        }

    metadata = latest_progress.metadata or {}

    return {
        "progress_seconds": metadata.get("progress_seconds", 0),
        "completion_rate": metadata.get("completion_rate", 0.0),
        "is_completed": metadata.get("completion_rate", 0.0) >= 0.9,
        "last_watched": latest_progress.created_at,
    }
