"""认证相关配置模块"""

from pydantic import Field
from pydantic_settings import BaseSettings


class AuthConfig(BaseSettings):
    """认证相关配置"""

    # JWT
    SECRET_KEY: str = Field(..., description="用于JWT令牌加密的密钥")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=60 * 24 * 8, description="访问令牌过期分钟数")

    # Device
    DEVICE_VERIFICATION_EXPIRE_MINUTES: int = Field(
        default=10, description="设备验证令牌过期时间（分钟）"
    )

    # SMS
    SIGN_NAME: str = Field(default="阿里云短信服务", description="短信签名")

    # WeChat
    WECHAT_APP_ID: str = Field(..., description="微信公众号AppID")
    WECHAT_APP_SECRET: str = Field(..., description="微信公众号AppSecret")
    WECHAT_QR_EXPIRE_SECONDS: int = Field(
        default=600, description="二维码过期时间（秒）", ge=60, le=2592000
    )
    WECHAT_QR_REDIS_PREFIX: str = Field(
        default="wechat:qr:", description="Redis中二维码状态的键前缀"
    )
    WECHAT_MAX_BIND_ATTEMPTS: int = Field(default=5, description="最大绑定尝试次数", ge=1, le=10)
    WECHAT_BIND_TIME_WINDOW: int = Field(
        default=3600, description="绑定尝试时间窗口（秒）", ge=300, le=86400
    )
    WECHAT_DEBUG_MODE: bool = Field(default=False, description="是否开启微信调试模式")
    WECHAT_MESSAGE_TOKEN: str = Field(..., description="微信公众号消息推送Token")
    WECHAT_MESSAGE_SECRET_KEY: str = Field(..., description="微信公众号消息推送SecretKey")

    class Config:
        env_file = [".env", ".env.dev"]
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"
        env_nested_delimiter = "__"
