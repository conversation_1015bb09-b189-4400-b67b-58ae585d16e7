import os

from celery import Celery
from celery.schedules import crontab

from app.config import settings

task_modules = []
for filename in os.listdir("app/tasks"):
    if filename.endswith(".py") and not filename.startswith("__"):
        module_name = f"app.tasks.{filename[:-3]}"  # 去掉 .py 后缀
        task_modules.append(module_name)

app = Celery(
    "steam_aggregation",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=task_modules,
)

app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    # 启用异步任务支持
    task_always_eager=False,
    worker_concurrency=4,
    # 支持异步任务结果
    result_backend_transport_options={
        "visibility_timeout": 3600,
        "master_name": settings.REDIS_SENTINEL_MASTER_NAME,
    },
    broker_transport_options={
        "master_name": settings.REDIS_SENTINEL_MASTER_NAME,
    },
    beat_schedule={
        # --- 高频增量任务 ---
        "update-item-profiles-incremental-hourly": {
            "task": "app.tasks.recommendation_tasks.task_update_item_profiles_incremental",
            "schedule": crontab(minute=0),  # 每小时的0分执行
        },
        "update-user-profiles-incremental-hourly": {
            "task": "app.tasks.recommendation_tasks.task_update_user_profiles_incremental",
            "schedule": crontab(minute=5),  # 每小时的5分执行
        },
        # --- 低频全量任务 ---
        "recluster-users-full-daily": {
            "task": "app.tasks.recommendation_tasks.task_recluster_users_full",
            "schedule": crontab(hour=2, minute=0),  # 每天凌晨2点执行
        },
        "regenerate-group-recommendations-full-daily": {
            "task": "app.tasks.recommendation_tasks.task_regenerate_group_recommendations_full",
            "schedule": crontab(hour=3, minute=0),  # 每天凌晨3点执行
        },
        # --- 其他现有任务 ---
        "sync-stats-to-db-every-hour": {
            "task": "tasks.sync_stats_to_db",
            "schedule": 3600.0,
        },
        "calculate-hot-content-hourly": {
            "task": "app.tasks.recommendation_tasks.task_calculate_hot_content",
            "schedule": 3600.0,
        },
        "calculate-item-similarity-daily": {
            "task": "app.tasks.recommendation_tasks.task_calculate_item_similarity",
            "schedule": crontab(hour=1, minute=0),
        },
        "cache-latest-content-hourly": {
            "task": "app.tasks.recommendation_tasks.task_cache_latest_content",
            "schedule": 3600.0,
        },
        # --- 用户缓存预热任务 ---
        "warm-up-user-cache-daily": {
            "task": "app.tasks.cache_tasks.task_warm_up_user_cache",
            "schedule": crontab(hour=4, minute=0),  # 每天凌晨4点执行
        },
        # --- 发件箱中继任务 ---
        "relay-outbox-messages-every-5-seconds": {
            "task": "app.tasks.cache_tasks.task_relay_outbox_messages",
            "schedule": 5.0,  # 每5秒执行一次
        },
        # --- 布隆过滤器维护任务 ---
        "rebuild-article-bloom-daily": {
            "task": "app.tasks.bloom_tasks.task_rebuild_article_bloom_filter",
            "schedule": crontab(hour=4, minute=15),  # 每天 04:15 UTC 执行
            "kwargs": {"drop_existing": True, "page_size": 2000},
        },
    },
)

app.autodiscover_tasks()
