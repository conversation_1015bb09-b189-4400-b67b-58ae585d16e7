from fastapi.encoders import jsonable_encoder
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.crud.base import CRUDBase
from app.models.comment import Comment, CommentType
from app.schemas.comment import CommentCreate, CommentUpdate


class CRUDComment(CRUDBase[Comment, CommentCreate, CommentUpdate]):
    async def create(self, db: AsyncSession, *, obj_in: CommentCreate, author_id: int) -> Comment:
        """创建评论"""
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data, author_id=author_id)
        # parent_id 和 reply_to_id 已经在 API 端点层处理过了，这里不需要重复处理
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_by_article(
        self, db: AsyncSession, *, article_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取文章的评论，扁平化结构"""
        query = (
            select(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
            .options(joinedload(self.model.author))
            .order_by(self.model.created_at.asc())
        )
        result = await db.execute(query)
        all_comments = result.scalars().all()

        # 手动分页
        top_level_comments = [c for c in all_comments if not c.parent_id]
        paginated_top_level = top_level_comments[skip : skip + limit]

        # 获取这些分页后的顶层评论的ID
        paginated_top_level_ids = {c.id for c in paginated_top_level}

        # 筛选出所有相关的回复（包括子评论的评论）
        relevant_replies = []
        remaining_comments = [c for c in all_comments if c.parent_id]
        parent_ids = paginated_top_level_ids

        while parent_ids and remaining_comments:
            current_level_replies = [c for c in remaining_comments if c.parent_id in parent_ids]
            if not current_level_replies:
                break
            relevant_replies.extend(current_level_replies)
            parent_ids = {c.id for c in current_level_replies}
            remaining_comments = [c for c in remaining_comments if c not in current_level_replies]

        return paginated_top_level + relevant_replies

    async def get_by_video(
        self, db: AsyncSession, *, video_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取视频的评论，扁平化结构"""
        query = (
            select(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
            .options(joinedload(self.model.author))
            .order_by(self.model.created_at.asc())
        )
        result = await db.execute(query)
        all_comments = result.scalars().all()

        # 手动分页
        top_level_comments = [c for c in all_comments if not c.parent_id]
        paginated_top_level = top_level_comments[skip : skip + limit]

        # 获取这些分页后的顶层评论的ID
        paginated_top_level_ids = {c.id for c in paginated_top_level}

        # 筛选出所有相关的回复（包括子评论的评论）
        relevant_replies = []
        remaining_comments = [c for c in all_comments if c.parent_id]
        parent_ids = paginated_top_level_ids

        while parent_ids and remaining_comments:
            current_level_replies = [c for c in remaining_comments if c.parent_id in parent_ids]
            if not current_level_replies:
                break
            relevant_replies.extend(current_level_replies)
            parent_ids = {c.id for c in current_level_replies}
            remaining_comments = [c for c in remaining_comments if c not in current_level_replies]

        return paginated_top_level + relevant_replies

    async def get_by_user(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取用户的评论列表"""
        query = (
            select(self.model)
            .filter(self.model.author_id == author_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def count_by_article(self, db: AsyncSession, *, article_id: int) -> int:
        """统计文章的评论数量"""
        query = (
            select(func.count())
            .select_from(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
        )
        result = await db.execute(query)
        return result.scalar_one()

    async def count_by_video(self, db: AsyncSession, *, video_id: int) -> int:
        """统计视频的评论数量"""
        query = (
            select(func.count())
            .select_from(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
        )
        result = await db.execute(query)
        return result.scalar_one()

    async def get_by_article_cursor(
        self, db: AsyncSession, *, article_id: int, params: CursorPaginationParams
    ) -> CursorPaginationResponse:
        """使用游标分页获取文章评论"""
        # 获取所有评论
        query = (
            select(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
            .options(joinedload(self.model.author))
            .order_by(self.model.created_at.desc())  # 按创建时间倒序
        )
        result = await db.execute(query)
        all_comments = result.scalars().all()

        # 分离顶层评论和回复
        top_level_comments = [c for c in all_comments if not c.parent_id]

        # 应用游标过滤到顶层评论
        if params.cursor:
            try:
                cursor_value = int(params.cursor)
                # 当cursor为0时，表示从头开始，不进行过滤
                # 因为是按创建时间倒序，所以游标过滤是 id < cursor_value
                if cursor_value > 0:
                    top_level_comments = [c for c in top_level_comments if c.id < cursor_value]
            except ValueError:
                pass

        # 分页顶层评论
        has_next = len(top_level_comments) > params.size
        paginated_top_level = top_level_comments[: params.size]

        # 获取这些分页后的顶层评论的ID
        paginated_top_level_ids = {c.id for c in paginated_top_level}

        # 筛选出所有相关的回复（包括子评论的评论）
        relevant_replies = []
        remaining_comments = [c for c in all_comments if c.parent_id]
        parent_ids = paginated_top_level_ids

        while parent_ids and remaining_comments:
            current_level_replies = [c for c in remaining_comments if c.parent_id in parent_ids]
            if not current_level_replies:
                break
            relevant_replies.extend(current_level_replies)
            parent_ids = {c.id for c in current_level_replies}
            remaining_comments = [c for c in remaining_comments if c not in current_level_replies]

        # 计算游标
        next_cursor = None
        if has_next and paginated_top_level:
            next_cursor = str(paginated_top_level[-1].id)

        return CursorPaginationResponse(
            items=paginated_top_level + relevant_replies,
            has_next=has_next,
            has_previous=bool(params.cursor),
            next_cursor=next_cursor,
            previous_cursor=None,  # 简化实现，不提供上一页游标
            total_count=None,  # 不计算总数以提高性能
        )

    async def get_by_video_cursor(
        self, db: AsyncSession, *, video_id: int, params: CursorPaginationParams
    ) -> CursorPaginationResponse:
        """使用游标分页获取视频评论"""
        # 获取所有评论
        query = (
            select(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
            .options(joinedload(self.model.author))
            .order_by(self.model.created_at.desc())  # 按创建时间倒序
        )
        result = await db.execute(query)
        all_comments = result.scalars().all()

        # 分离顶层评论和回复
        top_level_comments = [c for c in all_comments if not c.parent_id]

        # 应用游标过滤到顶层评论
        if params.cursor:
            try:
                cursor_value = int(params.cursor)
                # 当cursor为0时，表示从头开始，不进行过滤
                # 因为是按创建时间倒序，所以游标过滤是 id < cursor_value
                if cursor_value > 0:
                    top_level_comments = [c for c in top_level_comments if c.id < cursor_value]
            except ValueError:
                pass

        # 分页顶层评论
        has_next = len(top_level_comments) > params.size
        paginated_top_level = top_level_comments[: params.size]

        # 获取这些分页后的顶层评论的ID
        paginated_top_level_ids = {c.id for c in paginated_top_level}

        # 筛选出相关的回复
        relevant_replies = [c for c in all_comments if c.parent_id in paginated_top_level_ids]

        # 计算游标
        next_cursor = None
        if has_next and paginated_top_level:
            next_cursor = str(paginated_top_level[-1].id)

        return CursorPaginationResponse(
            items=paginated_top_level + relevant_replies,
            has_next=has_next,
            has_previous=bool(params.cursor),
            next_cursor=next_cursor,
            previous_cursor=None,  # 简化实现，不提供上一页游标
            total_count=None,  # 不计算总数以提高性能
        )

    async def get_comment_counts_batch(
        self, db: AsyncSession, content_items: list[tuple[str, int]]
    ) -> dict[tuple[str, int], int]:
        """批量获取内容的评论数。"""
        counts = {}
        for content_type, content_id in content_items:
            query = (
                select(func.count()).select_from(self.model).where(self.model.is_visible)
            )
            if content_type == "article":
                query = query.where(self.model.article_id == content_id)
            elif content_type == "video":
                query = query.where(self.model.video_id == content_id)
            else:
                continue

            result = await db.execute(query)
            count = result.scalar_one()
            counts[(content_type, content_id)] = count
        return counts

    async def bulk_update_stats(
        self, db: AsyncSession, stats_data: dict[int, dict[str, int]]
    ) -> None:
        """评论的统计数据目前不在模型中，此方法为空实现。"""
        pass


comment = CRUDComment(Comment)
