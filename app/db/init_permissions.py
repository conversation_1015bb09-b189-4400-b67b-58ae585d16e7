from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.permission_system import clear_all_permissions_cache
from app.models.user import Permission, User, UserRole


async def init_permissions(db: AsyncSession) -> None:
    # 清除权限缓存
    clear_all_permissions_cache()
    """初始化权限数据"""

    # 创建基本权限
    permissions = [
        # 用户资源权限
        Permission(
            name="查看所有用户",
            code="user:read:all",
            resource="user",
            action="read",
            description="允许查看所有用户",
        ),
        Permission(
            name="查看个人信息",
            code="user:read:own",
            resource="user",
            action="read",
            description="允许查看自己的信息",
        ),
        Permission(
            name="创建用户",
            code="user:create:all",
            resource="user",
            action="create",
            description="允许创建新用户",
        ),
        Permission(
            name="更新个人信息",
            code="user:update:own",
            resource="user",
            action="update",
            description="允许更新自己的信息",
        ),
        Permission(
            name="更新所有用户",
            code="user:update:all",
            resource="user",
            action="update",
            description="允许更新所有用户信息",
        ),
        Permission(
            name="删除用户",
            code="user:delete:all",
            resource="user",
            action="delete",
            description="允许删除用户",
        ),
        Permission(
            name="关注用户",
            code="user:follow:all",
            resource="user",
            action="follow",
            description="允许关注其他用户",
        ),
        Permission(
            name="管理用户",
            code="user:manage:all",
            resource="user",
            action="manage",
            description="允许管理用户",
        ),
        # 角色资源权限
        Permission(
            name="查看所有角色",
            code="role:read:all",
            resource="role",
            action="read",
            description="允许查看所有角色",
        ),
        Permission(
            name="创建角色",
            code="role:create:all",
            resource="role",
            action="create",
            description="允许创建新角色",
        ),
        Permission(
            name="更新角色",
            code="role:update:all",
            resource="role",
            action="update",
            description="允许更新角色信息",
        ),
        Permission(
            name="删除角色",
            code="role:delete:all",
            resource="role",
            action="delete",
            description="允许删除角色",
        ),
        Permission(
            name="管理角色",
            code="role:manage:all",
            resource="role",
            action="manage",
            description="允许管理角色",
        ),
        # 权限资源权限
        Permission(
            name="查看所有权限",
            code="permission:read:all",
            resource="permission",
            action="read",
            description="允许查看所有权限",
        ),
        Permission(
            name="管理权限",
            code="permission:manage:all",
            resource="permission",
            action="manage",
            description="允许管理权限",
        ),
        # 文章资源权限
        Permission(
            name="查看公开文章",
            code="article:read:public",
            resource="article",
            action="read",
            description="允许查看已发布且已审核的文章",
        ),
        Permission(
            name="查看所有文章",
            code="article:read:all",
            resource="article",
            action="read",
            description="允许查看所有文章",
        ),
        Permission(
            name="查看个人文章",
            code="article:read:own",
            resource="article",
            action="read",
            description="允许查看自己创建的文章",
        ),
        Permission(
            name="创建文章",
            code="article:create:own",
            resource="article",
            action="create",
            description="允许创建新文章",
        ),
        Permission(
            name="更新个人文章",
            code="article:update:own",
            resource="article",
            action="update",
            description="允许更新自己创建的文章",
        ),
        Permission(
            name="更新所有文章",
            code="article:update:all",
            resource="article",
            action="update",
            description="允许更新任何文章",
        ),
        Permission(
            name="发布个人文章",
            code="article:publish:own",
            resource="article",
            action="publish",
            description="允许发布自己创建的文章",
        ),
        Permission(
            name="发布所有文章",
            code="article:publish:all",
            resource="article",
            action="publish",
            description="允许发布任何文章",
        ),
        Permission(
            name="审核文章",
            code="article:approve:all",
            resource="article",
            action="approve",
            description="允许审核文章",
        ),
        Permission(
            name="删除个人文章",
            code="article:delete:own",
            resource="article",
            action="delete",
            description="允许删除自己创建的文章",
        ),
        Permission(
            name="删除所有文章",
            code="article:delete:all",
            resource="article",
            action="delete",
            description="允许删除任何文章",
        ),
        Permission(
            name="管理文章",
            code="article:manage:all",
            resource="article",
            action="manage",
            description="允许管理文章系统",
        ),
        # 评论资源权限
        Permission(
            name="查看公开评论",
            code="comment:read:public",
            resource="comment",
            action="read",
            description="允许查看公开评论",
        ),
        Permission(
            name="查看所有评论",
            code="comment:read:all",
            resource="comment",
            action="read",
            description="允许查看所有评论",
        ),
        Permission(
            name="查看个人评论",
            code="comment:read:own",
            resource="comment",
            action="read",
            description="允许查看自己创建的评论",
        ),
        Permission(
            name="创建评论",
            code="comment:create:own",
            resource="comment",
            action="create",
            description="允许创建新评论",
        ),
        Permission(
            name="更新个人评论",
            code="comment:update:own",
            resource="comment",
            action="update",
            description="允许更新自己创建的评论",
        ),
        Permission(
            name="更新所有评论",
            code="comment:update:all",
            resource="comment",
            action="update",
            description="允许更新任何评论",
        ),
        Permission(
            name="删除个人评论",
            code="comment:delete:own",
            resource="comment",
            action="delete",
            description="允许删除自己创建的评论",
        ),
        Permission(
            name="删除所有评论",
            code="comment:delete:all",
            resource="comment",
            action="delete",
            description="允许删除任何评论",
        ),
        Permission(
            name="管理评论",
            code="comment:manage:all",
            resource="comment",
            action="manage",
            description="允许管理评论系统",
        ),
        # 视频资源权限
        Permission(
            name="查看公开视频",
            code="video:read:public",
            resource="video",
            action="read",
            description="允许查看已发布且已审核的视频",
        ),
        Permission(
            name="查看所有视频",
            code="video:read:all",
            resource="video",
            action="read",
            description="允许查看所有视频",
        ),
        Permission(
            name="查看个人视频",
            code="video:read:own",
            resource="video",
            action="read",
            description="允许查看自己创建的视频",
        ),
        Permission(
            name="创建视频",
            code="video:create:own",
            resource="video",
            action="create",
            description="允许创建新视频",
        ),
        Permission(
            name="更新个人视频",
            code="video:update:own",
            resource="video",
            action="update",
            description="允许更新自己创建的视频",
        ),
        Permission(
            name="更新所有视频",
            code="video:update:all",
            resource="video",
            action="update",
            description="允许更新任何视频",
        ),
        Permission(
            name="发布个人视频",
            code="video:publish:own",
            resource="video",
            action="publish",
            description="允许发布自己创建的视频",
        ),
        Permission(
            name="发布所有视频",
            code="video:publish:all",
            resource="video",
            action="publish",
            description="允许发布任何视频",
        ),
        Permission(
            name="审核视频",
            code="video:approve:all",
            resource="video",
            action="approve",
            description="允许审核视频",
        ),
        Permission(
            name="删除个人视频",
            code="video:delete:own",
            resource="video",
            action="delete",
            description="允许删除自己创建的视频",
        ),
        Permission(
            name="删除所有视频",
            code="video:delete:all",
            resource="video",
            action="delete",
            description="允许删除任何视频",
        ),
        Permission(
            name="管理视频",
            code="video:manage:all",
            resource="video",
            action="manage",
            description="允许管理视频系统",
        ),
    ]

    # 检查并创建权限
    for perm_data in permissions:
        result = await db.execute(select(Permission).where(Permission.code == perm_data.code))
        if not result.scalar_one_or_none():
            db.add(perm_data)
    await db.commit()

    # 检查并创建角色
    roles_to_check = ["管理员", "普通用户", "访客"]
    for role_name in roles_to_check:
        result = await db.execute(select(UserRole).where(UserRole.name == role_name))
        if not result.scalar_one_or_none():
            if role_name == "管理员":
                db.add(UserRole(name="管理员", desc="系统管理员，拥有所有权限", is_active=True))
            elif role_name == "普通用户":
                db.add(
                    UserRole(
                        name="普通用户",
                        desc="普通用户，拥有基本查看权限",
                        is_default=True,
                        is_active=True,
                    )
                )
            elif role_name == "访客":
                db.add(UserRole(name="访客", desc="访客用户，只有有限的查看权限", is_active=True))
    await db.commit()

    # 分配权限给角色
    # 管理员拥有所有权限
    result = await db.execute(select(UserRole).where(UserRole.name == "管理员"))
    admin_role = result.scalar_one()
    await db.refresh(admin_role, ["permissions"])
    result = await db.execute(select(Permission))
    all_permissions = result.scalars().all()
    admin_role.permissions = all_permissions

    # 为普通用户分配权限
    result = await db.execute(select(UserRole).where(UserRole.name == "普通用户"))
    user_role = result.scalar_one()
    await db.refresh(user_role, ["permissions"])
    user_permission_codes = {
        "user:read:own",
        "user:update:own",
        "user:follow:all",
        "article:read:public",
        "article:read:own",
        "article:create:own",
        "article:update:own",
        "article:publish:own",
        "article:delete:own",
        "comment:read:public",
        "comment:read:own",
        "comment:create:own",
        "comment:update:own",
        "comment:delete:own",
        "video:read:public",
        "video:read:own",
        "video:create:own",
        "video:update:own",
        "video:publish:own",
        "video:delete:own",
    }
    result = await db.execute(select(Permission).where(Permission.code.in_(user_permission_codes)))
    user_role.permissions = result.scalars().all()

    # 为访客分配权限
    result = await db.execute(select(UserRole).where(UserRole.name == "访客"))
    guest_role = result.scalar_one()
    await db.refresh(guest_role, ["permissions"])
    guest_permission_codes = {
        "article:read:public",
        "user:read:all",  # 允许查看用户公开信息
        "comment:read:public",  # 允许查看公开评论
        "video:read:public",  # 允许访客查看公开视频
    }
    result = await db.execute(select(Permission).where(Permission.code.in_(guest_permission_codes)))
    guest_role.permissions = result.scalars().all()

    await db.commit()

    # 创建超级管理员用户（如果不存在）
    result = await db.execute(select(User).where(User.username == "admin"))
    if not result.scalar_one_or_none():
        result = await db.execute(select(UserRole).where(UserRole.name == "管理员"))
        admin_role = result.scalar_one()
        admin_user = User(
            username="admin",
            password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            nickname="超级管理员",
            email="<EMAIL>",
            role_id=admin_role.id,
            is_active=True,
            is_superuser=True,
        )
        db.add(admin_user)
        await db.commit()
