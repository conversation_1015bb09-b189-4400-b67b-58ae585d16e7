import logging

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.config import settings

logger = logging.getLogger(__name__)

# 创建异步SQLAlchemy引擎
engine = create_async_engine(
    settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
    # echo=True,  # 设置为True可以在控制台查看SQL语句，用于调试
    future=True,
)

# 创建异步会话工厂
SessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# 创建Base类，所有模型都将继承这个类
Base = declarative_base()


async def get_db():
    """获取数据库会话的依赖函数
    Yields:
        Session: 数据库会话实例
    """
    db = SessionLocal()
    try:
        # 验证连接是否有效
        await db.execute(text("SELECT 1"))
        yield db
    except Exception:
        raise
    finally:
        await db.close()
