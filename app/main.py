"""应用主入口"""

import uuid

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from loguru import logger

from app.api.api import api_router
from app.api.behavior_tracking_middleware import BehaviorTrackingMiddleware

# 微信公众号消息推送api
from app.api.middleware import ResponseFormatterMiddleware
from app.api.visit_stats_middleware import VisitStatsMiddleware
from app.config import settings
from app.core.init_data import init_data
from app.core.limiter import setup_limiter
from app.core.logging import logger
from app.core.pagination import add_pagination
from app.db.redis import get_redis_master
from app.exception_handlers.registry import register_exception_handlers


async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 初始化Redis连接
    redis = await get_redis_master()
    app.state.redis = redis
    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")

    # 初始化应用数据
    logger.info("正在初始化应用数据...")
    await init_data()
    logger.info("应用数据初始化完成")

    yield


# 创建FastAPI应用实例
app = FastAPI(
    title="Steam数据聚合API",
    description="提供Steam数据的聚合和分析服务",
    version="0.1.0",
    lifespan=lifespan,
    # 生产默认关闭默认文档
    # openapi_url=f"{settings.API_V1_STR}/openapi.json",
)

# 初始化分页功能
add_pagination(app)

# 初始化限流器
setup_limiter(app)

# 初始化日志
logger.info("应用程序启动中...")


# 添加请求ID中间件
@app.middleware("http")
async def request_id_middleware(request: Request, call_next):
    """为每个请求注入一个唯一的ID，以便追踪"""
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    with logger.contextualize(request_id=request_id):
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        return response


# 添加用户行为追踪中间件
app.add_middleware(BehaviorTrackingMiddleware)

# 添加访问统计中间件
app.add_middleware(VisitStatsMiddleware)

# 添加响应格式化中间件
app.add_middleware(ResponseFormatterMiddleware)

# 配置CORS（最后添加，最先执行）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://192.168.31.145:3000"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册所有异常处理器
register_exception_handlers(app)


@app.get("/")
async def root():
    """API根路径，返回欢迎信息"""
    return {"message": "欢迎使用Steam数据聚合API"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy"}


# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
