# 使models目录成为一个Python包
from app.models.article import Article
from app.models.banner import Banner
from app.models.category import Category
from app.models.comment import Comment
from app.models.favorite import Favorite
from app.models.file_hash import FileHash
from app.models.history import History
from app.models.like import Like
from app.models.review import Review, ReviewStatus
from app.models.tag import Tag
from app.models.user import Permission, User, UserRole
from app.models.user_behavior import (
    ContentSimilarity,
    RecommendationLog,
    UserBrowseHistory,
    UserInteraction,
    UserProfile,
)
from app.models.user_device import UserDevice
from app.models.user_stats import UserStats
from app.models.video import Video
from app.models.video_folder import VideoFolder

from .outbox import OutboxMessage

# 在这里导入所有模型，以便在其他地方可以通过app.models导入


def load_all_models():
    """
    This function doesn't do anything, but by importing and calling it,
    we ensure that all model modules are loaded and registered with SQLAlchemy's
    metadata before Alembic tries to access it. This is a workaround for
    aggressive linters that might remove "unused" model imports.
    """
    pass


__all__ = [
    "Category",
    "Article",
    "Comment",
    "Favorite",
    "FileHash",
    "Like",
    "Review",
    "ReviewStatus",
    "Tag",
    "User",
    "UserRole",
    "Video",
    "VideoFolder",
    "Permission",
    "UserBrowseHistory",
    "UserInteraction",
    "UserProfile",
    "RecommendationLog",
    "ContentSimilarity",
    "UserDevice",
    "History",
    "Banner",
    "UserStats",
    "OutboxMessage",
]
