from datetime import UTC, datetime

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    and_,
)
from sqlalchemy.orm import foreign, relationship
from sqlalchemy.types import TypeDecorator
from sqlalchemy.dialects.postgresql import TIMESTAMP as PostgresTIMESTAMP

from app.db.session import Base
from app.models.review import ContentType, Review


class Timestamp(TypeDecorator):
    """支持时区的DateTime类型"""
    impl = PostgresTIMESTAMP(timezone=True)
    cache_ok = True


class Article(Base):
    """文章数据模型"""

    __tablename__ = "articles"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), index=True, nullable=True)
    content = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    cover_url = Column(String(512), nullable=True, comment="封面图URL")
    author_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    is_published = Column(Boolean, default=False, comment="是否发布 如果未发布则显示到草稿箱中")
    is_approved = Column(Boolean, default=False, comment="是否通过审核 仅在发布后需要审核")
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    visit_count = Column(Integer, default=0, comment="访问次数")
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True)
    updated_at = Column(
        Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=True
    )
    # 分类
    slug = Column(String(255), index=True, nullable=True)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    cache_version = Column(Integer, nullable=False, server_default="1", comment="缓存版本号")

    # 关联关系
    author = relationship("User", back_populates="articles")
    comments = relationship("Comment", back_populates="article")
    category = relationship("Category", back_populates="articles")
    tags = relationship("Tag", secondary="article_tags", back_populates="articles")

    # 关联到审核记录
    # 这是一个单向的关系，因为Review模型是多态的（用于文章和视频）
    # 我们使用 primaryjoin 来显式定义连接条件
    reviews = relationship(
        "Review",
        primaryjoin=lambda: and_(
            Article.id == foreign(Review.content_id),
            Review.content_type == ContentType.ARTICLE,
        ),
        cascade="all, delete-orphan",
        viewonly=True,  # 将此关系标记为只读，因为它没有直接的 back_populates
    )
