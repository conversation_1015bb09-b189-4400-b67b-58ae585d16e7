from datetime import datetime, timezone

from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class Favorite(Base):
    """收藏数据模型"""

    __tablename__ = "favorites"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    
    # 内容类型：article 或 video
    content_type = Column(String(20), nullable=False, index=True)
    
    # 内容ID（文章ID或视频ID）
    content_id = Column(Integer, nullable=False, index=True)
    
    # 收藏备注（可选）
    note = Column(Text, nullable=True)
    
    # 是否有效（用于软删除）
    is_active = Column(Boolean, default=True, index=True)
    
    created_at = Column(Timestamp, default=now_utc, index=True)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)

    # 关联关系
    user = relationship("User")

    # 联合唯一约束：同一用户对同一内容只能收藏一次
    __table_args__ = (
        UniqueConstraint("user_id", "content_type", "content_id", name="uq_user_content_favorite"),
    )

    def __repr__(self):
        return f"<Favorite user_id={self.user_id} {self.content_type}:{self.content_id}>"
