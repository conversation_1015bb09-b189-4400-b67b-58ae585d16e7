"""文件哈希表"""

from datetime import datetime, timezone

from sqlalchemy import (
    JSON,
    Column,
    DateTime,
    Integer,
    String,
    UniqueConstraint,
)

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class FileHash(Base):
    __tablename__ = "file_hashes"

    id = Column(Integer, primary_key=True, index=True)
    # oss 文件路径
    file_path = Column(String(255), nullable=False, index=True)
    file_hash = Column(String(255), nullable=False, index=True)
    file_metadata = Column(JSON, nullable=True)  # 存储文件元数据，如时长、分辨率、封面URL
    created_at = Column(Timestamp, default=now_utc)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)

    # 联合唯一约束：同一文件路径只能有一个哈希值
    __table_args__ = (UniqueConstraint("file_hash", name="uq_file_path"),)

    def __repr__(self):
        return f"<FileHash {self.file_path}>"
