from datetime import datetime, timezone

from sqlalchemy import (
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class History(Base):
    """历史数据模型"""

    __tablename__ = "history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    content_type = Column(String(20), nullable=False, index=True)
    content_id = Column(Integer, nullable=False, index=True)
    created_at = Column(Timestamp, default=now_utc, index=True)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)
    last_visited_at = Column(Timestamp, default=now_utc)  # 新增：最后访问时间
    visit_count = Column(Integer, default=1)  # 新增：访问次数

    # 关联关系
    user = relationship("User")

    # 联合唯一约束：同一用户对同一内容只能记录一次
    __table_args__ = (
        UniqueConstraint("user_id", "content_type", "content_id", name="uq_user_content_history"),
    )

    def __repr__(self):
        return f"<History user_id={self.user_id} {self.content_type}:{self.content_id}>"
