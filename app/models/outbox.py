from datetime import datetime, timezone

from sqlalchemy import Column, DateTime, Integer, String
from sqlalchemy.dialects.postgresql import JSONB

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class OutboxMessage(Base):
    """
    事务性发件箱模型，用于保证数据库事务和消息传递的原子性。
    """

    __tablename__ = "outbox_messages"

    id = Column(Integer, primary_key=True, index=True)
    topic = Column(String(100), nullable=False, index=True, comment="消息主题，如 user.updated")
    payload = Column(JSONB, nullable=False, comment="消息内容")
    status = Column(
        String(20),
        nullable=False,
        default="pending",
        index=True,
        comment="消息状态 (pending, sent, failed)",
    )
    created_at = Column(Timestamp, default=now_utc, nullable=False)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc, nullable=False)

    def __repr__(self):
        return f"<OutboxMessage {self.id} on topic {self.topic} with status {self.status}>"
