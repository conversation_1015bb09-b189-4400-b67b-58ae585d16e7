from datetime import datetime, timezone

from sqlalchemy import (
    <PERSON>olean,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Table,
)
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc

# 文章-标签关联表
article_tags = Table(
    "article_tags",
    Base.metadata,
    Column("article_id", Integer, ForeignKey("articles.id"), primary_key=True),
    Column("tag_id", Integer, ForeignKey("tags.id"), primary_key=True),
    Column("created_at", Timestamp, default=now_utc),
)

# 视频-标签关联表
video_tags = Table(
    "video_tags",
    Base.metadata,
    Column("video_id", Integer, ForeignKey("videos.id"), primary_key=True),
    Column("tag_id", Integer, ForeignKey("tags.id"), primary_key=True),
    Column("created_at", Timestamp, default=now_utc),
)


class Tag(Base):
    """标签数据模型"""

    __tablename__ = "tags"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True, nullable=False)
    is_default = Column(Boolean, default=False)
    created_at = Column(Timestamp, default=now_utc)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)

    # 关联关系
    articles = relationship("Article", secondary=article_tags, back_populates="tags")
    videos = relationship("Video", secondary=video_tags, back_populates="tags")

    def __repr__(self):
        return f"<Tag {self.name}>"
