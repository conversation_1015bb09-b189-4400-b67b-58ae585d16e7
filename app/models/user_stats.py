from datetime import datetime, timezone

from sqlalchemy import Column, DateTime, ForeignKey, Integer
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class UserStats(Base):
    """用户统计数据模型"""

    __tablename__ = "user_stats"

    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), primary_key=True)

    # 收到的总点赞数
    total_likes_count = Column(Integer, nullable=False, default=0, server_default="0")
    # 收到的总收藏数
    total_favorites_count = Column(Integer, nullable=False, default=0, server_default="0")
    # 关注总数
    following_count = Column(Integer, nullable=False, default=0, server_default="0")
    # 粉丝总数
    follower_count = Column(Integer, nullable=False, default=0, server_default="0")
    # 文章总数
    article_count = Column(Integer, nullable=False, default=0, server_default="0")
    # 视频总数
    video_count = Column(Integer, nullable=False, default=0, server_default="0")

    # 关联关系
    user = relationship("User", back_populates="stats")

    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc, nullable=False)

    def __repr__(self):
        return f"<UserStats of user_id={self.user_id}>"
