from datetime import UTC, datetime

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    and_,
)
from sqlalchemy.orm import foreign, relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc
from app.models.review import ContentType, Review


class Video(Base):
    """视频数据模型"""

    __tablename__ = "videos"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), index=True, nullable=False)
    slug = Column(String(255), unique=True, index=True, nullable=True, comment="视频别名")
    description = Column(Text, nullable=True)
    url = Column(String(512), nullable=True, comment="视频URL")
    cover_url = Column(String(512), nullable=True, comment="封面图URL")
    duration = Column(Integer, nullable=True, comment="视频时长（秒）")
    width = Column(Integer, nullable=True, comment="视频宽度（像素）")
    height = Column(Integer, nullable=True, comment="视频高度（像素）")
    author_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    # 是否发布 如果未发布则显示到草稿箱中
    is_published = Column(Boolean, default=False)
    is_approved = Column(Boolean, default=False, comment="是否通过审核 仅在发布后需要审核")
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    visit_count = Column(Integer, default=0, comment="访问次数")
    # 排序权重
    sort_order = Column(Integer, default=0, comment="排序权重，值越大越靠前")
    # 软删除标记
    created_at = Column(Timestamp, default=now_utc, nullable=True)
    updated_at = Column(
        Timestamp, default=now_utc, onupdate=now_utc, nullable=True
    )
    deleted_at = Column(Timestamp, nullable=True)
    # 分类
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    # 所属文件夹 - 不再允许为空，每个视频必须属于一个文件夹
    folder_id = Column(Integer, ForeignKey("video_folders.id"), nullable=False, index=True)
    cache_version = Column(Integer, nullable=False, server_default="1", comment="缓存版本号")

    # 关联关系
    author = relationship("User", back_populates="videos")
    comments = relationship("Comment", back_populates="video")
    category = relationship("Category", back_populates="videos")
    banners = relationship("Banner", back_populates="video")
    tags = relationship("Tag", secondary="video_tags", back_populates="videos")
    folder = relationship("VideoFolder", back_populates="videos")

    # 关联到审核记录
    # 这是一个单向的关系，因为Review模型是多态的（用于文章和视频）
    # 我们使用 primaryjoin 来显式定义连接条件
    reviews = relationship(
        "Review",
        primaryjoin=lambda: and_(
            Video.id == foreign(Review.content_id),
            Review.content_type == ContentType.VIDEO,
        ),
        cascade="all, delete-orphan",
        viewonly=True,  # 将此关系标记为只读，因为它没有直接的 back_populates
    )

    def __repr__(self):
        return f"<Video {self.title}>"
