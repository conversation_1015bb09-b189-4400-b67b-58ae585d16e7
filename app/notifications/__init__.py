"""
消息通知服务模块
提供实时消息推送功能
"""

from .models import Notification, NotificationType, NotificationPriority, NotificationStatus
from .schemas import (
    NotificationCreate,
    NotificationUpdate,
    NotificationResponse,
    NotificationListResponse,
    UnreadCountResponse,
    WebSocketMessage,
    NewNotificationMessage,
    UnreadCountMessage,
    SystemNotificationMessage
)

__all__ = [
    "Notification",
    "NotificationType",
    "NotificationPriority",
    "NotificationStatus",
    "NotificationCreate",
    "NotificationUpdate",
    "NotificationResponse",
    "NotificationListResponse",
    "UnreadCountResponse",
    "WebSocketMessage",
    "NewNotificationMessage",
    "UnreadCountMessage",
    "SystemNotificationMessage"
]