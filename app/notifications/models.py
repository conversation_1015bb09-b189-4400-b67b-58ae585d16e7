from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Enum, JSON
from sqlalchemy.orm import relationship
from app.db.session import Base
import enum
from datetime import datetime

class NotificationType(enum.Enum):
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    TASK_COMPLETE = "task_complete"
    NEW_MESSAGE = "new_message"
    SYSTEM_UPDATE = "system_update"
    CONTENT_RECOMMENDATION = "content_recommendation"
    ACCOUNT_ACTIVITY = "account_activity"

class NotificationPriority(enum.Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class NotificationStatus(enum.Enum):
    UNREAD = "unread"
    READ = "read"
    DELETED = "deleted"

class Notification(Base):
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    type = Column(Enum(NotificationType), nullable=False, index=True)
    priority = Column(Enum(NotificationPriority), default=NotificationPriority.NORMAL, index=True)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    status = Column(Enum(NotificationStatus), default=NotificationStatus.UNREAD, index=True)
    data = Column(JSON)  # 附加数据，如任务ID、内容ID等
    action_url = Column(String(500))  # 可点击的链接
    expires_at = Column(DateTime)  # 过期时间
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    read_at = Column(DateTime)
    
    # 关系
    user = relationship("User", back_populates="notifications")
    
    def __repr__(self):
        return f"<Notification(id={self.id}, user_id={self.user_id}, type={self.type}, title='{self.title}')>"