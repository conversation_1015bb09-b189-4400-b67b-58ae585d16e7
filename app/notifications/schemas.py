from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from app.notifications.models import NotificationType, NotificationPriority, NotificationStatus

class NotificationBase(BaseModel):
    type: NotificationType
    priority: NotificationPriority = NotificationPriority.NORMAL
    title: str = Field(..., min_length=1, max_length=200)
    message: str = Field(..., min_length=1)
    data: Optional[Dict[str, Any]] = None
    action_url: Optional[str] = Field(None, max_length=500)

class NotificationCreate(NotificationBase):
    user_id: int
    expires_in_hours: Optional[int] = Field(None, ge=1, le=168)  # 最多7天

class NotificationUpdate(BaseModel):
    status: Optional[NotificationStatus] = None
    read_at: Optional[datetime] = None

class NotificationResponse(NotificationBase):
    id: int
    status: NotificationStatus
    created_at: datetime
    read_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class NotificationListResponse(BaseModel):
    notifications: list[NotificationResponse]
    total: int
    unread_count: int
    
class UnreadCountResponse(BaseModel):
    unread_count: int
    
class WebSocketMessage(BaseModel):
    type: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
class NewNotificationMessage(WebSocketMessage):
    type: str = "new_notification"
    data: NotificationResponse
    
class UnreadCountMessage(WebSocketMessage):
    type: str = "unread_count"
    data: Dict[str, int]
    
class SystemNotificationMessage(WebSocketMessage):
    type: str = "system_notification"
    data: Dict[str, Any]