"""认证相关的数据传输对象 (DTOs)"""

from dataclasses import dataclass
from datetime import datetime

from pydantic import BaseModel, Field

from app.models.user_device import UserDevice
from app.schemas.user import UserAggregated


class AuthenticationResult(BaseModel):
    """认证结果数据传输对象"""

    success: bool = Field(..., description="认证是否成功")
    user: UserAggregated | None = Field(None, description="认证成功的用户信息")
    access_token: str | None = Field(None, description="访问令牌 (JWT)")
    requires_device_verification: bool = Field(False, description="是否需要进行设备验证")
    verification_token: str | None = Field(None, description="用于设备验证的临时令牌")
    message: str = Field("", description="相关的消息或错误详情")
    auth_method: str | None = Field(None, description="本次认证使用的方法")


@dataclass
class DeviceTrustEvaluation:
    """设备信任评估结果"""

    device: UserDevice
    is_trusted: bool
    requires_verification: bool
    trust_score: int
    risk_factors: list[str] = None

    def __post_init__(self):
        if self.risk_factors is None:
            self.risk_factors = []


@dataclass
class DeviceVerificationPayload:
    """设备验证载荷"""

    user_id: int
    device_id: int
    device_fingerprint: str
    phone_number: str
    issued_at: datetime
    expires_at: datetime


class AuthInitiationResponse(BaseModel):
    """认证发起响应"""

    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: dict | None = Field(None, description="附加数据")
    auth_type: str | None = Field(None, description="认证类型")


class AuthCompletionResponse(BaseModel):
    """认证完成响应"""

    success: bool = Field(..., description="是否成功")
    user: UserAggregated | None = Field(None, description="用户信息")
    access_token: str | None = Field(None, description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    requires_device_verification: bool = Field(False, description="是否需要设备验证")
    verification_token: str | None = Field(None, description="设备验证令牌")
    message: str = Field("", description="响应消息")
    auth_method: str | None = Field(None, description="认证方法")


class QRCodeResponse(BaseModel):
    """二维码响应模型"""

    scene_str: str = Field(..., description="场景字符串")
    qr_url: str = Field(..., description="二维码图片URL")
    expire_seconds: int = Field(..., description="过期时间（秒）")


class SMSVerificationRequest(BaseModel):
    """短信验证请求"""

    phone: str = Field(..., description="手机号", min_length=11, max_length=11)
    template_type: str = Field(..., description="短信模板类型")


class SMSVerificationResponse(BaseModel):
    """短信验证响应"""

    success: bool = Field(..., description="是否成功")
    status: str = Field(..., description="状态")
    message: str = Field(..., description="响应消息")
    data: dict | None = Field(None, description="附加数据")
    timestamp: str = Field(..., description="时间戳")
    retry_after: int | None = Field(None, description="重试间隔（秒）")


class DeviceVerificationRequest(BaseModel):
    """设备验证请求"""

    verification_token: str = Field(..., description="验证令牌")
    verification_code: str = Field(..., description="验证码")
