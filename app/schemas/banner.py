from datetime import datetime

from pydantic import BaseModel


class BannerBase(BaseModel):
    """轮播图基础模型"""

    title: str
    description: str | None = None
    image_url: str
    link_url: str | None = None
    video_id: int | None = None
    sort_order: int = 0
    is_active: bool = True
    banner_type: str = "home"


class BannerCreate(BannerBase):
    """创建轮播图模型"""

    pass


class BannerUpdate(BaseModel):
    """更新轮播图模型"""

    title: str | None = None
    description: str | None = None
    image_url: str | None = None
    link_url: str | None = None
    video_id: int | None = None
    sort_order: int | None = None
    is_active: bool | None = None
    banner_type: str | None = None


class BannerInDBBase(BannerBase):
    """数据库中的轮播图模型"""

    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Banner(BannerInDBBase):
    """API响应中的轮播图模型"""

    pass


class BannerWithVideo(Banner):
    """包含视频信息的轮播图模型"""

    video: dict | None = None
