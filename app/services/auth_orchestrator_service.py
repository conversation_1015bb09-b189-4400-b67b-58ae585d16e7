"""认证编排服务 (AuthOrchestratorService)

作为整个认证系统的核心枢纽，负责统一处理和协调所有认证相关的流程。

主要职责:
- **统一入口**: 为所有认证方式（如短信、微信、密码等）提供统一的调用入口。
- **流程编排**: 协调不同认证服务（SMSAuthService,
    WeChatAuthService等）之间的协作，完成复杂的认证流程。
- **状态管理**: 管理和查询认证过程中的状态，特别是对于异步流程（如微信扫码登录）。
- **设备验证**: 集成设备信任服务（DeviceTrustService），处理新设备登录的安全验证。
- **错误处理**: 提供统一的认证错误处理机制和响应格式。
- **依赖管理**: 通过依赖注入管理所有底层服务（Token, User, Device等）。
"""

from typing import Any

from fastapi import Depends, Request, Response
from fastapi.responses import PlainTextResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.enums import AuthType
from app.core.logging import logger
from app.core.response_wrapper import ResponseCode
from app.core.sms_template import SmsTemplate
from app.exceptions.auth import (
    AuthenticationError,
    DeviceVerificationError,
    SMSVerificationError,
    WeChatAuthError,
)
from app.schemas.auth import AuthCompletionResponse, AuthenticationResult, AuthInitiationResponse
from app.schemas.wechat import WeChatScanStatus
from app.services.interfaces.device_service_interface import IDeviceTrustService
from app.services.interfaces.sms_auth_service_interface import ISMSAuthService
from app.services.interfaces.token_service_interface import ITokenService
from app.services.interfaces.user_management_service_interface import IUserManagementService
from app.services.interfaces.wechat_auth_service_interface import IWeChatAuthService
from app.services.service_factory import (
    get_device_trust_service,
    get_sms_auth_service,
    get_token_service,
    get_user_management_service,
    get_wechat_auth_service,
)
from app.utils.auth_helpers import AuthResponseBuilder


class AuthOrchestratorService:
    """
    认证编排服务类

    实现了认证流程的统一管理和调度。
    """

    def __init__(
        self,
        token_service: ITokenService = Depends(get_token_service),
        device_trust_service: IDeviceTrustService = Depends(get_device_trust_service),
        user_management_service: IUserManagementService = Depends(get_user_management_service),
        sms_auth_service: ISMSAuthService = Depends(get_sms_auth_service),
        wechat_auth_service: IWeChatAuthService = Depends(get_wechat_auth_service),
    ):
        """
        初始化认证编排服务及其所有依赖项。

        通过FastAPI的依赖注入机制，自动装配所有需要的服务接口实现。

        Args:
            token_service (ITokenService): 令牌服务，用于创建和验证Token。
            device_trust_service (IDeviceTrustService): 设备信任服务，管理设备指纹和信任状态。
            user_management_service (IUserManagementService): 用户管理服务，
            处理用户创建、查询和更新。
            sms_auth_service (ISMSAuthService): 短信认证服务，处理验证码发送和验证。
            wechat_auth_service (IWeChatAuthService): 微信认证服务，处理微信扫码、回调和绑定。
        """
        self.sms_auth_service = sms_auth_service
        self.wechat_auth_service = wechat_auth_service
        self.device_service = device_trust_service
        self.user_management_service = user_management_service
        self.token_service = token_service
        self.auth_utils = AuthResponseBuilder
        self.logger = logger

    async def initiate_authentication(
        self, auth_type: AuthType, request_data: dict[str, Any], request: Request | None = None
    ) -> AuthInitiationResponse:
        """
        发起一个认证流程。

        根据指定的认证类型，调用相应的服务来启动认证过程。
        例如，对于SMS，它会发送验证码；对于微信，它会生成一个用于扫码的二维码。

        Args:
            auth_type (AuthType): 要发起的认证类型 (e.g., SMS, WECHAT)。
            request_data (dict[str, Any]): 发起认证所需的特定数据。
                - 对于SMS: `{"phone": "...", "template_type": "LOGIN" | "REGISTER"}`
                - 对于微信: `{}` (无特定数据)
            request (Request | None): FastAPI的请求对象，可选。

        Returns:
            AuthInitiationResponse: 包含认证发起结果的响应对象。
                - 对于SMS，包含成功信息。
                - 对于微信，包含二维码URL和场景字符串。

        Raises:
            AuthenticationError: 如果认证类型不受支持或在发起过程中发生未知错误。
            SMSVerificationError: 如果在发起SMS认证时发生特定错误（如手机号无效）。
            WeChatAuthError: 如果在创建微信二维码时发生错误。
        """
        self.logger.info(f"发起认证流程 - 类型: {auth_type.value}")

        if auth_type == AuthType.SMS:
            return await self._initiate_sms_auth(request_data)
        elif auth_type == AuthType.WECHAT:
            return await self._initiate_wechat_auth(request_data)
        else:
            raise AuthenticationError(
                message=f"不支持的认证类型: {auth_type.value}",
                response_code=ResponseCode.BAD_REQUEST,
            )

    async def complete_authentication(
        self,
        auth_type: AuthType,
        verification_data: dict[str, Any],
        request: Request,
        db: AsyncSession,
    ) -> AuthCompletionResponse:
        """
        完成一个认证流程。

        根据认证类型，使用用户提供的验证数据（如验证码）来完成认证。
        成功后，会检查设备信任状态，并可能返回一个需要设备验证的响应。

        Args:
            auth_type (AuthType): 正在完成的认证类型。
            verification_data (dict[str, Any]): 用于验证的数据。
                - 对于SMS: `{"phone": "...", "code": "...", "template_type": "..."}`
                - 对于微信绑定: `{"openid": "...", "phone": "...", "verification_code": "..."}`
            request (Request): FastAPI的请求对象，用于设备指纹识别。
            db (AsyncSession): 数据库会话。

        Returns:
            AuthCompletionResponse: 包含最终认证结果的响应，可能包括JWT令牌或设备验证请求。

        Raises:
            AuthenticationError: 如果认证类型不受支持或发生未知错误。
            SMSVerificationError: 如果SMS验证码无效或已过期。
            WeChatAuthError: 如果微信绑定失败。
            DeviceVerificationError: 如果在设备验证环节出现问题。
        """
        try:
            self.logger.info(f"完成认证流程 - 类型: {auth_type.value}")

            if auth_type == AuthType.SMS:
                result = await self._complete_sms_auth(verification_data, request, db)
            elif auth_type == AuthType.WECHAT:
                result = await self._complete_wechat_auth(verification_data, request, db)
            else:
                raise AuthenticationError(
                    message=f"不支持的认证类型: {auth_type.value}",
                    response_code=ResponseCode.BAD_REQUEST,
                )

            return self._convert_to_completion_response(result, auth_type.value)

        except (SMSVerificationError, WeChatAuthError, DeviceVerificationError, RateLimitError):
            # 重新抛出具体的认证错误
            raise
        except Exception as e:
            self.logger.error(f"完成认证流程失败: {str(e)}")
            raise AuthenticationError(
                message="认证流程完成失败，请稍后重试",
                response_code=ResponseCode.INTERNAL_SERVER_ERROR,
            ) from e

    async def handle_device_verification(
        self, verification_token: str, verification_code: str, db: AsyncSession
    ) -> AuthCompletionResponse:
        """
        处理新设备的验证流程。

        当一个用户在新设备上成功完成初步认证后，系统会要求进行设备验证。
        此方法使用短信验证码来确认用户身份，并将该设备标记为受信任。

        Args:
            verification_token (str): 在上一步认证成功后颁发的设备验证令牌。
            verification_code (str): 用户收到的用于验证设备的短信验证码。
            db (AsyncSession): 数据库会话。

        Returns:
            AuthCompletionResponse: 设备验证成功后，返回包含JWT令牌的最终认证响应。

        Raises:
            DeviceVerificationError: 如果验证令牌无效、验证码错误或流程中发生其他错误。
            SMSVerificationError: 如果底层的短信验证服务报告错误。
        """
        try:
            self.logger.info("开始处理设备验证流程")
            self.logger.info(
                f"设备验证令牌: {verification_token[:20]}...{verification_token[-10:] if len(verification_token) > 30 else verification_token}"
            )
            self.logger.info(f"用户输入验证码: {verification_code}")

            # 使用SMS认证服务处理设备验证
            result = await self.sms_auth_service.verify_device_code_and_complete_auth(
                verification_token, verification_code, db
            )

            self.logger.info("设备验证流程完成成功")
            return self._convert_to_completion_response(result, "device_verification")

        except (SMSVerificationError, DeviceVerificationError) as e:
            self.logger.error(f"设备验证失败 - 类型: {type(e).__name__}, 消息: {str(e)}")
            # 重新抛出具体的验证错误
            raise
        except Exception as e:
            self.logger.error(f"设备验证流程失败: {str(e)}", exc_info=True)
            raise DeviceVerificationError(message="设备验证失败，请稍后重试") from e

    async def get_authentication_status(
        self, auth_type: AuthType, identifier: str, request: Request, db: AsyncSession
    ) -> WeChatScanStatus:
        """
        获取异步认证流程的当前状态。

        主要用于轮询微信扫码登录的状态。客户端可以使用二维码的场景字符串(scene_str)
        来查询用户是否已扫码、是否已确认登录或是否需要绑定手机。

        Args:
            auth_type (AuthType): 要查询的认证类型 (目前仅支持 WECHAT)。
            identifier (str): 认证流程的唯一标识符，对于微信登录即为场景字符串 (scene_str)。
            request (Request): FastAPI请求对象，用于设备指纹识别。
            db (AsyncSession): 数据库会话。

        Returns:
            dict[str, Any]: 包含当前状态信息的字典。
        """
        if auth_type != AuthType.WECHAT:
            raise AuthenticationError(
                message=f"认证类型 {auth_type.value} 不支持状态查询",
                response_code=ResponseCode.BAD_REQUEST,
            )
        try:
            self.logger.debug(f"获取扫码状态 - scene: {identifier}")
            # 直接返回 Pydantic 模型，FastAPI 会自动处理序列化
            return await self.wechat_auth_service.check_scan_status(identifier, request, db)
        except Exception as e:
            self.logger.error(f"获取扫码状态失败: {e}", exc_info=True)
            raise AuthenticationError(
                message="获取扫码状态失败", response_code=ResponseCode.INTERNAL_SERVER_ERROR
            ) from e

    # --- 私有方法：SMS认证流程 ---

    async def _initiate_sms_auth(self, request_data: dict[str, Any]) -> AuthInitiationResponse:
        """发起SMS认证"""
        # 验证请求数据
        phone = request_data.get("phone")
        template_type_str = request_data.get("template_type", "LOGIN")

        if not phone:
            raise SMSVerificationError(message="手机号不能为空", details={"phone": phone})

        # 转换模板类型
        try:
            template_type = SmsTemplate[template_type_str]
        except KeyError:
            template_type = SmsTemplate.LOGIN

        # 发送验证码
        response = await self.sms_auth_service.send_verification_code(phone, template_type)

        return AuthInitiationResponse(
            success=response.success,
            message=response.message,
            data=None,
            auth_type=AuthType.SMS.value,
        )

    async def _complete_sms_auth(
        self, verification_data: dict[str, Any], request: Request, db: AsyncSession
    ) -> AuthenticationResult:
        """完成SMS认证"""
        phone = verification_data.get("phone")
        code = verification_data.get("code")
        template_type_str = verification_data.get("template_type", "LOGIN")

        if not phone or not code:
            raise SMSVerificationError(message="手机号和验证码不能为空", details={"phone": phone})

        # 转换模板类型
        try:
            template_type = SmsTemplate[template_type_str]
        except KeyError:
            template_type = SmsTemplate.LOGIN

        # 验证并完成认证
        return await self.sms_auth_service.verify_code_and_authenticate(
            phone, code, template_type, request, db
        )

    # --- 私有方法：微信认证流程 ---

    async def _initiate_wechat_auth(self, request_data: dict[str, Any]) -> AuthInitiationResponse:
        """发起微信认证"""
        try:
            # 创建二维码
            qr_response = await self.wechat_auth_service.create_qr_code()

            return AuthInitiationResponse(
                success=True,
                message="二维码创建成功",
                data={
                    "scene_str": qr_response.scene_str,
                    "qr_url": qr_response.qr_url,
                    "expire_seconds": qr_response.expire_seconds,
                },
                auth_type=AuthType.WECHAT.value,
            )

        except WeChatAuthError:
            raise
        except Exception as e:
            raise WeChatAuthError(message="微信认证发起失败") from e

    async def _complete_wechat_auth(
        self, verification_data: dict[str, Any], request: Request, db: AsyncSession
    ) -> AuthenticationResult:
        """完成微信认证（绑定手机号）"""
        openid = verification_data.get("openid")
        phone = verification_data.get("phone")
        code = verification_data.get("code")

        if not openid or not phone or not code:
            raise WeChatAuthError(
                message="openid、手机号和验证码不能为空",
                details={"openid": openid, "phone": phone},
            )

        return await self.wechat_auth_service.bind_phone_number(
            openid=openid,
            phone=phone,
            verification_code=code,
            request=request,
            db=db,
        )

    # --- 微信扫码登录专用方法 ---

    async def generate_wechat_qr_code(self) -> dict[str, Any]:
        """生成微信扫码登录二维码"""
        try:
            self.logger.info("生成微信扫码登录二维码")
            qr_response = await self.wechat_auth_service.create_qr_code()
            return {
                "scene_str": qr_response.scene_str,
                "qr_url": qr_response.qr_url,
                "expire_seconds": qr_response.expire_seconds,
            }
        except Exception as e:
            self.logger.error(f"生成二维码失败: {str(e)}")
            raise WeChatAuthError(message="生成二维码失败") from e

    async def handle_wechat_scan(self, openid: str, scene_str: str) -> AuthenticationResult:
        """处理微信扫码事件"""
        try:
            self.logger.info(f"处理微信扫码 - openid: {openid}, scene: {scene_str}")
            return await self.wechat_auth_service.handle_scan_event(openid, scene_str)
        except Exception as e:
            self.logger.error(f"处理扫码事件失败: {str(e)}")
            raise WeChatAuthError(
                message="处理扫码事件失败", details={"openid": openid, "scene_str": scene_str}
            ) from e

    async def get_wechat_scan_status(
        self, scene_str: str, request: Request, db: AsyncSession
    ) -> dict[str, Any]:
        """获取微信扫码状态"""
        return await self.get_authentication_status(AuthType.WECHAT, scene_str, request, db)

    async def handle_wechat_callback(self, request: Request) -> Response:
        """
        处理来自微信服务器的统一回调请求。

        此方法会根据HTTP方法分派任务：
        - GET: 处理微信服务器的URL验证。
        - POST: 处理微信服务器推送的事件消息。

        Args:
            request (Request): FastAPI的请求对象。

        Returns:
            Response: 根据处理结果返回相应的响应对象。
        """
        if request.method == "GET":
            # 处理微信服务器验证
            params = request.query_params
            signature = params.get("signature")
            timestamp = params.get("timestamp")
            nonce = params.get("nonce")
            echostr = params.get("echostr")

            if not all([signature, timestamp, nonce, echostr]):
                return PlainTextResponse(content="Missing required parameters", status_code=400)

            # 直接调用底层服务进行验证
            if self.wechat_auth_service.wechat_integration.verify_signature(
                signature, timestamp, nonce
            ):
                self.logger.info("微信服务器验证成功")
                return PlainTextResponse(content=echostr)
            else:
                self.logger.warning("微信服务器验证失败")
                return PlainTextResponse(content="Signature verification failed", status_code=403)

        elif request.method == "POST":
            # 处理事件推送
            params = request.query_params
            signature = params.get("signature")
            timestamp = params.get("timestamp")
            nonce = params.get("nonce")

            if not all([signature, timestamp, nonce]):
                return PlainTextResponse(
                    content="Missing required signature parameters", status_code=400
                )

            xml_data = await request.body()

            try:
                response_xml = await self.wechat_auth_service.handle_wechat_callback(
                    xml_data.decode("utf-8"), signature, timestamp, nonce
                )
                return Response(content=response_xml, media_type="application/xml")
            except Exception as e:
                self.logger.error(f"处理微信事件回调时发生严重错误: {e}", exc_info=True)
                # 即使内部出错，也向微信返回成功XML，避免微信重试风暴
                return Response(content="success", media_type="application/xml")

        return PlainTextResponse(content="Unsupported method", status_code=405)

    # --- 私有方法：响应转换 ---

    def _convert_to_completion_response(
        self, result: AuthenticationResult, auth_method: str
    ) -> AuthCompletionResponse:
        """将认证结果转换为完成响应"""

        return AuthCompletionResponse(
            status="success" if result.success else "error",
            success=result.success,
            user=result.user,
            access_token=result.access_token,
            token_type="bearer",
            requires_device_verification=result.requires_device_verification,
            verification_token=result.verification_token,
            message=result.message,
            auth_method=auth_method,
        )


# 全局认证编排服务实例将在 service_factory 中创建
