"""短信认证服务 (SMSAuthService)

提供完整的短信认证功能，遵循单一职责原则。

主要职责:
- **验证码管理**: 负责发送登录、注册和设备验证等场景的短信验证码。
- **安全验证**: 验证用户提交的验证码，并处理错误尝试和频率限制。
- **核心认证流程**: 在验证码验证成功后，执行完整的认证逻辑，包括：
    - 自动注册新用户。
    - 评估设备信任状态。
    - 在设备受信任时颁发JWT令牌。
    - 在检测到新设备时启动设备验证流程。
"""

import re

from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.core.sms_template import SmsTemplate
from app.exceptions.auth import (
    AuthErrorCodes,
    SMSVerificationError,
)
from app.schemas.auth import AuthenticationResult, SMSVerificationResponse
from app.services.interfaces.device_service_interface import IDeviceTrustService
from app.services.interfaces.sms_auth_service_interface import ISMSAuthService
from app.services.interfaces.sms_service_interface import ISmsService
from app.services.interfaces.token_service_interface import ITokenService
from app.services.interfaces.user_management_service_interface import IUserManagementService
from app.services.user_aggregation_service import UserAggregationService


class SMSAuthService(ISMSAuthService):
    """
    短信认证服务实现类。
    """

    def __init__(
        self,
        token_service: ITokenService,
        device_trust_service: IDeviceTrustService,
        user_management_service: IUserManagementService,
        user_aggregation_service: UserAggregationService,
        sms_service: "ISmsService",
    ):
        """
        初始化短信认证服务。

        Args:
            token_service (ITokenService): 令牌服务实例。
            device_trust_service (IDeviceTrustService): 设备信任服务实例。
            user_management_service (IUserManagementService): 用户管理服务实例。
            user_aggregation_service (UserAggregationService): 用户聚合服务实例。
            sms_service (ISmsService): 短信服务实例。
        """
        self.sms_service = sms_service
        self.device_service = device_trust_service
        self.user_management_service = user_management_service
        self.token_service = token_service
        self.user_aggregation_service = user_aggregation_service
        self.logger = logger

    async def send_verification_code(
        self, phone: str, template_type: SmsTemplate
    ) -> SMSVerificationResponse:
        """
        验证手机号格式并发送短信验证码。

        Args:
            phone (str): 目标手机号码。
            template_type (SmsTemplate): 短信模板类型 (e.g., LOGIN, REGISTER)。

        Returns:
            SMSVerificationResponse: 包含操作成功与否及消息的响应对象。

        Raises:
            RateLimitError: 如果请求过于频繁。
            SMSVerificationError: 如果手机号格式无效或发生其他发送错误。
        """

        # 验证手机号格式
        validated_phone = self._validate_phone_number(phone)

        # 发送验证码
        await self.sms_service.send_verification_code(validated_phone, template_type)

        self.logger.info(
            f"短信验证码发送成功 - 手机号: {validated_phone}, 模板: {template_type.name}"
        )

        return SMSVerificationResponse(
            success=True,
            status="success",
            message="验证码发送成功，请查收短信",
            data=None,
        )

    async def verify_code_and_authenticate(
        self, phone: str, code: str, template_type: SmsTemplate, request: Request, db: AsyncSession
    ) -> AuthenticationResult:
        """
        验证短信验证码并执行完整的认证流程。

        这是短信认证的核心方法，其流程如下：
        1. 验证手机号和验证码的有效性。
        2. 如果用户不存在，则根据手机号自动创建新用户。
        3. 评估当前设备的信任状态。
        4. 如果设备受信任，则生成并返回JWT访问令牌。
        5. 如果是新设备，则不返回令牌，而是返回一个设备验证令牌，启动设备验证流程。

        Args:
            phone (str): 用户手机号。
            code (str): 用户提交的短信验证码。
            template_type (SmsTemplate): 验证码对应的模板类型。
            request (Request): FastAPI请求对象，用于获取设备信息。
            db (AsyncSession): 数据库会话。

        Returns:
            AuthenticationResult: 包含认证结果的对象。根据设备信任状态，可能包含
                                  访问令牌或设备验证令牌。

        Raises:
            SMSVerificationError: 如果验证码无效或在认证过程中发生严重错误。
        """
        # 验证手机号格式
        validated_phone = self._validate_phone_number(phone)

        # 验证短信验证码
        is_valid = await self._verify_sms_code(validated_phone, code, template_type)
        if not is_valid:
            return AuthenticationResult(success=False, message="验证码错误或已过期")

        # 获取或创建用户
        is_new_user, user = await self.user_management_service.get_or_create_user_by_phone(
            validated_phone, db
        )

        # 评估设备信任状态
        trust_evaluation = await self.device_service.evaluate_device_trust(user, request, db)

        # 如果需要设备验证
        if trust_evaluation.requires_verification:
            verification_token = await self.device_service.generate_device_verification_token(
                user_id=user.id,
                device_id=trust_evaluation.device.id,
                device_fingerprint=trust_evaluation.device.device_fingerprint,
                phone_number=validated_phone,
            )

            self.logger.info(
                f"用户 {user.username} 需要设备验证 - 设备ID: {trust_evaluation.device.id}"
            )

            return AuthenticationResult(
                success=True,
                user=None,
                access_token=None,
                requires_device_verification=True,
                verification_token=verification_token,
                message="检测到新设备登录，需要进行安全验证",
                auth_method="sms",
            )

        # 设备受信任，完成认证
        access_token = await self.token_service.create_access_token(
            data={"sub": user.username}, device_id=trust_evaluation.device.id
        )

        # 获取聚合用户信息
        aggregated_user = await self.user_aggregation_service.get_user_profile(db, user.id)

        # 更新用户最后登录时间
        await self.user_management_service.update_last_login(user.id, db)

        self.logger.info(
            f"用户 {user.username} 短信认证成功 - 新用户: {is_new_user}, "
            f"设备ID: {trust_evaluation.device.id}"
        )

        return AuthenticationResult(
            success=True,
            user=aggregated_user,
            access_token=access_token,
            requires_device_verification=False,
            verification_token=None,
            message="登录成功" if not is_new_user else "注册并登录成功",
            auth_method="sms",
        )

    async def verify_device_code_and_complete_auth(
        self, verification_token: str, code: str, db: AsyncSession
    ) -> AuthenticationResult:
        """
        验证用于新设备验证的短信验证码，并完成最终认证。

        此方法用于处理新设备登录的最后一步。
        1. 验证设备验证令牌的有效性。
        2. 验证用户为新设备提供的短信验证码。
        3. 如果验证通过，将该设备标记为受信任。
        4. 生成并返回最终的JWT访问令牌。

        Args:
            verification_token (str): 由上一步 `verify_code_and_authenticate` 生成的设备验证令牌。
            code (str): 用户收到的用于设备验证的短信验证码。
            db (AsyncSession): 数据库会话。

        Returns:
            AuthenticationResult: 包含最终认证结果和JWT访问令牌的响应对象。

        Raises:
            SMSVerificationError: 如果设备令牌无效、验证码错误或流程失败。
        """
        # 验证设备令牌
        self.logger.info(f"开始验证设备令牌: {verification_token[:20]}...")
        payload = await self.device_service.verify_device_token(verification_token)
        self.logger.info(
            f"设备令牌验证成功 - 手机号: {payload.phone_number}, 用户ID: {payload.user_id}, 设备ID: {payload.device_id}"
        )

        # 验证短信验证码
        self.logger.info(
            f"开始验证短信验证码 - 手机号: {payload.phone_number}, 验证码: {code}, 模板类型: {SmsTemplate.DEVICE_VERIFICATION}"
        )
        is_valid = await self._verify_sms_code(
            payload.phone_number, code, SmsTemplate.DEVICE_VERIFICATION
        )
        self.logger.info(f"短信验证码验证结果: {is_valid}")
        if not is_valid:
            self.logger.warning(
                f"短信验证码验证失败 - 手机号: {payload.phone_number}, 输入验证码: {code}"
            )
            raise SMSVerificationError(
                message="验证码错误或已过期",
                error_code=AuthErrorCodes.INVALID_VERIFICATION_CODE,
            )

        # 获取用户和设备
        user = await self.user_management_service.get_user_by_id(payload.user_id, db)
        if not user:
            raise SMSVerificationError(
                message="用户不存在", error_code=AuthErrorCodes.USER_NOT_FOUND
            )

        # 信任设备
        device = await self.device_service.trust_device(payload.device_id, db)

        # 生成访问令牌
        access_token = await self.token_service.create_access_token(
            data={"sub": user.username}, device_id=device.id
        )

        # 获取聚合用户信息
        aggregated_user = await self.user_aggregation_service.get_user_profile(db, user.id)

        # 更新用户最后登录时间
        await self.user_management_service.update_last_login(user.id, db)

        self.logger.info(f"用户 {user.username} 设备验证成功 - 设备ID: {device.id}")

        return AuthenticationResult(
            success=True,
            user=aggregated_user,
            access_token=access_token,
            requires_device_verification=False,
            verification_token=None,
            message="设备验证成功，已登录",
            auth_method="sms",
        )

    def _validate_phone_number(self, phone: str) -> str:
        """
        验证手机号格式

        Args:
            phone: 手机号字符串

        Returns:
            str: 验证通过的手机号

        Raises:
            SMSVerificationError: 手机号格式不正确
        """
        if not phone:
            raise SMSVerificationError(
                message="手机号不能为空",
                error_code=AuthErrorCodes.INVALID_PHONE_NUMBER,
                phone=phone,
            )

        # 中国大陆手机号正则表达式
        pattern = r"^1[3-9]\d{9}$"
        if not re.match(pattern, phone):
            raise SMSVerificationError(
                message="手机号格式不正确",
                error_code=AuthErrorCodes.INVALID_PHONE_NUMBER,
                phone=phone,
            )

        return phone

    async def _verify_sms_code(self, phone: str, code: str, template_type: SmsTemplate) -> bool:
        """
        验证短信验证码

        Args:
            phone: 手机号
            code: 验证码
            template_type: 短信模板类型

        Returns:
            bool: 验证是否成功

        Raises:
            SMSVerificationError: 验证相关错误
        """
        self.logger.info(
            f"_verify_sms_code 被调用 - 手机号: {phone}, 验证码: {code}, 模板类型: {template_type}"
        )
        try:
            result = await self.sms_service.verify_code(phone, code, template_type)
            self.logger.info(f"sms_service.verify_code 返回结果: {result}")
            return result
        except ValueError as e:
            error_msg = str(e)
            self.logger.warning(f"验证码验证出现ValueError - 错误信息: {error_msg}")
            if "验证码错误次数过多" in error_msg:
                self.logger.error(f"验证码错误次数过多 - 手机号: {phone}")
                raise SMSVerificationError(
                    message="验证码错误次数过多，请重新获取验证码",
                    error_code=AuthErrorCodes.TOO_MANY_ATTEMPTS,
                    phone=phone,
                ) from e
            else:
                # 其他验证错误
                self.logger.warning(f"验证码验证失败 - 手机号: {phone}, 错误: {error_msg}")
                return False
        except Exception as e:
            self.logger.error(f"验证码验证过程中发生错误: {str(e)}", exc_info=True)
            raise SMSVerificationError(
                message="验证码验证失败，请稍后重试",
                error_code=AuthErrorCodes.SMS_SERVICE_UNAVAILABLE,
                phone=phone,
            ) from e


# 全局SMS认证服务实例将在 service_factory 中创建
