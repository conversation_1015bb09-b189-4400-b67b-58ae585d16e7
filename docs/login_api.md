# 登录API文档

本文档提供了系统登录相关的完整API接口说明，帮助前端开发者快速集成认证功能。

## 概述

系统支持多种登录方式：
- **短信验证码登录**：通过手机号和短信验证码进行登录/注册
- **微信扫码登录**：PC端通过微信扫码进行登录
- **设备安全验证**：新设备登录时的二次验证机制

## 基础信息

- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: Bearer Token (JWT)
- **响应格式**: JSON

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 错误响应
```json
{
  "code": "AUTH_SMS_001",
  "message": "验证码错误",
  "details": {
    "phone": "13800138000"
  }
}
```

---

## 1. 短信验证码登录

### 1.1 发送短信验证码

**端点**: `POST /auth/sms/send-code`

**描述**: 向指定手机号发送用于登录或注册的短信验证码

**请求体**:
```json
{
  "phone": "13800138000",
  "template_type": "LOGIN"
}
```

**参数说明**:
- `phone` (string, required): 目标手机号码，11位数字
- `template_type` (string, optional): 短信模板类型，默认为 "LOGIN"

**响应示例**:
```json
{
  "success": true,
  "message": "验证码已发送",
  "data": null,
  "auth_type": "SMS"
}
```

**错误码**:
- `AUTH_SMS_000`: 无效的手机号码
- `AUTH_SMS_007`: 短信发送频率过高
- `AUTH_SMS_003`: 短信服务不可用
- `AUTH_SMS_004`: 短信发送失败

### 1.2 短信登录/注册

**端点**: `POST /auth/sms/login`

**描述**: 使用手机号和短信验证码进行登录或注册

**请求体**:
```json
{
  "phone": "13800138000",
  "code": "123456"
}
```

**参数说明**:
- `phone` (string, required): 手机号码
- `code` (string, required): 收到的6位短信验证码

**响应示例** (正常登录):
```json
{
  "success": true,
  "user": {
    "id": 1,
    "username": "user_13800138000",
    "phone": "13800138000",
    "email": null,
    "avatar_url": null,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": null
  },
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "requires_device_verification": false,
  "verification_token": null,
  "message": "登录成功",
  "auth_method": "SMS"
}
```

**响应示例** (需要设备验证):
```json
{
  "success": true,
  "user": null,
  "access_token": null,
  "token_type": "bearer",
  "requires_device_verification": true,
  "verification_token": "device_verification_token_123",
  "message": "检测到新设备登录，请完成设备验证",
  "auth_method": "SMS"
}
```

**错误码**:
- `AUTH_SMS_001`: 验证码错误
- `AUTH_SMS_002`: 验证码已过期
- `AUTH_USER_001`: 用户不存在
- `AUTH_USER_002`: 用户账户已被禁用

---

## 2. 设备安全验证

### 2.1 确认新设备登录

**端点**: `POST /auth/device/confirm`

**描述**: 在新设备上完成二次安全验证

**请求体**:
```json
{
  "verification_token": "device_verification_token_123",
  "code": "654321"
}
```

**参数说明**:
- `verification_token` (string, required): 上一步认证成功后获取的设备验证令牌
- `code` (string, required): 用于设备验证的6位短信验证码

**响应示例**:
```json
{
  "success": true,
  "user": {
    "id": 1,
    "username": "user_13800138000",
    "phone": "13800138000",
    "email": null,
    "avatar_url": null,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": null
  },
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "requires_device_verification": false,
  "verification_token": null,
  "message": "设备验证成功",
  "auth_method": "SMS"
}
```

**错误码**:
- `AUTH_DEVICE_003`: 设备验证令牌无效
- `AUTH_DEVICE_009`: 设备验证失败
- `AUTH_SMS_001`: 设备验证码错误

---

## 3. 微信扫码登录

### 3.1 创建微信登录二维码

**端点**: `POST /wechat/qr-code/create`

**描述**: 为PC端扫码登录流程创建一个有时效性的二维码

**请求体**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "二维码创建成功",
  "data": {
    "scene_str": "wechat_login_123456",
    "qr_url": "https://api.example.com/qrcode/wechat_login_123456",
    "expire_seconds": 300
  },
  "auth_type": "WECHAT"
}
```

### 3.2 获取二维码扫描状态

**端点**: `GET /wechat/qr-code/status/{scene_str}`

**描述**: 前端在展示二维码后，应定期轮询此接口以获取最新状态

**路径参数**:
- `scene_str` (string, required): 创建二维码时返回的场景字符串

**响应示例** (等待扫码):
```json
{
  "status": "waiting",
  "openid": null,
  "user_info": null,
  "message": "等待用户扫码"
}
```

**响应示例** (已扫码):
```json
{
  "status": "scanned",
  "openid": "oxxxxxxxxxxxxxxxxxxxxxxx",
  "user_info": {
    "nickname": "用户昵称",
    "avatar": "https://thirdwx.qlogo.cn/xxx"
  },
  "message": "用户已扫码，请在手机上确认"
}
```

**响应示例** (需要绑定手机号):
```json
{
  "status": "bind_phone",
  "openid": "oxxxxxxxxxxxxxxxxxxxxxxx",
  "user_info": {
    "nickname": "用户昵称",
    "avatar": "https://thirdwx.qlogo.cn/xxx"
  },
  "message": "新用户需要绑定手机号"
}
```

**响应示例** (确认登录):
```json
{
  "status": "confirmed",
  "openid": "oxxxxxxxxxxxxxxxxxxxxxxx",
  "user_info": {
    "id": 1,
    "username": "user_13800138000",
    "phone": "13800138000",
    "avatar_url": "https://thirdwx.qlogo.cn/xxx",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": null
  },
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "message": "登录成功"
}
```

**响应示例** (二维码过期):
```json
{
  "status": "expired",
  "openid": null,
  "user_info": null,
  "message": "二维码已过期"
}
```

**状态说明**:
- `waiting`: 等待用户扫码
- `scanned`: 用户已扫码，前端应提示用户在手机上确认
- `bind_phone`: 用户是新用户，需要绑定手机号
- `confirmed`: 用户已确认登录，响应中包含访问令牌
- `expired`: 二维码已过期

### 3.3 绑定手机号并完成登录

**端点**: `POST /wechat/bind`

**描述**: 在用户扫码且状态为 `bind_phone` 后，调用此接口完成最终的账号绑定和登录

**请求体**:
```json
{
  "openid": "oxxxxxxxxxxxxxxxxxxxxxxx",
  "phone": "13800138000",
  "code": "123456"
}
```

**参数说明**:
- `openid` (string, required): 微信用户的OpenID，在扫码后由状态查询接口返回
- `phone` (string, required): 要绑定的手机号码
- `code` (string, required): 手机收到的6位验证码

**响应示例**:
```json
{
  "success": true,
  "user": {
    "id": 1,
    "username": "user_13800138000",
    "phone": "13800138000",
    "email": null,
    "avatar_url": "https://thirdwx.qlogo.cn/xxx",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": null
  },
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "requires_device_verification": false,
  "verification_token": null,
  "message": "绑定成功",
  "auth_method": "WECHAT"
}
```

---

## 4. 完整登录流程

### 4.1 短信登录流程

```mermaid
sequenceDiagram
    participant Frontend as 前端
    participant Backend as 后端
    participant SMS as 短信服务
    participant User as 用户

    Frontend->>Backend: POST /auth/sms/send-code
    Backend->>SMS: 发送验证码
    SMS->>User: 接收验证码
    Backend-->>Frontend: 发送成功
    
    Frontend->>User: 输入验证码
    Frontend->>Backend: POST /auth/sms/login
    
    alt 正常登录
        Backend-->>Frontend: 返回 access_token
    else 需要设备验证
        Backend-->>Frontend: 返回 verification_token
        Frontend->>User: 提示输入设备验证码
        Frontend->>Backend: POST /auth/device/confirm
        Backend-->>Frontend: 返回 access_token
    end
```

### 4.2 微信扫码登录流程

```mermaid
sequenceDiagram
    participant Frontend as 前端
    participant Backend as 后端
    participant WeChat as 微信
    participant User as 用户

    Frontend->>Backend: POST /wechat/qr-code/create
    Backend-->>Frontend: 返回二维码信息
    Frontend->>User: 显示二维码
    
    User->>WeChat: 扫码
    WeChat->>Backend: 通知扫码结果
    Backend-->>Frontend: 状态更新为 "scanned"
    
    User->>WeChat: 确认登录
    WeChat->>Backend: 通知确认结果
    
    alt 老用户
        Backend-->>Frontend: 状态更新为 "confirmed"，返回 access_token
    else 新用户
        Backend-->>Frontend: 状态更新为 "bind_phone"
        Frontend->>User: 显示绑定手机号界面
        Frontend->>Backend: POST /wechat/bind
        Backend-->>Frontend: 返回 access_token
    end
```

---

## 5. 错误处理

### 5.1 常见错误码

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| `AUTH_SMS_001` | 验证码错误 | 提示用户重新输入验证码 |
| `AUTH_SMS_002` | 验证码已过期 | 提示用户重新获取验证码 |
| `AUTH_SMS_007` | 短信发送频率过高 | 提示用户稍后再试 |
| `AUTH_USER_001` | 用户不存在 | 系统会自动注册，无需特殊处理 |
| `AUTH_USER_002` | 用户账户已被禁用 | 提示用户联系客服 |
| `AUTH_DEVICE_003` | 设备验证令牌无效 | 重新发起登录流程 |
| `AUTH_DEVICE_009` | 设备验证失败 | 提示用户重新输入验证码 |
| `AUTH_WECHAT_001` | 微信API调用失败 | 提示用户稍后再试 |
| `AUTH_WECHAT_003` | 二维码已过期 | 重新生成二维码 |

### 5.2 错误响应格式

```json
{
  "code": "AUTH_SMS_001",
  "message": "验证码错误",
  "details": {
    "phone": "13800138000"
  }
}
```

---

## 6. 安全建议

### 6.1 前端安全措施

1. **HTTPS**: 所有API请求必须使用HTTPS协议
2. **Token存储**: 将access_token存储在安全的地方（如HttpOnly Cookie或localStorage）
3. **验证码输入**: 限制验证码输入次数，防止暴力破解
4. **二维码轮询**: 合理设置轮询间隔（建议2-3秒）

### 6.2 用户引导

1. **验证码输入**: 提供倒计时功能，防止用户频繁发送验证码
2. **设备验证**: 清晰说明设备验证的目的和流程
3. **微信扫码**: 提供清晰的操作指引和状态提示

---

## 7. 测试用例

### 7.1 短信登录测试

```javascript
// 1. 发送验证码
const sendCodeResponse = await fetch('/auth/sms/send-code', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    phone: '13800138000',
    template_type: 'LOGIN'
  })
});

// 2. 登录
const loginResponse = await fetch('/auth/sms/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    phone: '13800138000',
    code: '123456'
  })
});
```

### 7.2 微信扫码登录测试

```javascript
// 1. 创建二维码
const qrResponse = await fetch('/wechat/qr-code/create', {
  method: 'POST'
});
const { scene_str, qr_url } = await qrResponse.json();

// 2. 轮询状态
const checkStatus = setInterval(async () => {
  const statusResponse = await fetch(`/wechat/qr-code/status/${scene_str}`);
  const status = await statusResponse.json();
  
  if (status.status === 'bind_phone') {
    clearInterval(checkStatus);
    // 显示绑定手机号界面
  } else if (status.status === 'confirmed') {
    clearInterval(checkStatus);
    // 登录成功，保存token
  }
}, 3000);
```

---

## 8. 相关文档

- [响应处理规范](../response_wrapper.py)
- [异常处理规范](../exceptions/auth.py)
- [数据模型定义](../schemas/auth.py)
- [服务层架构](../services/auth_orchestrator_service.py)

---

**注意**: 本文档基于当前系统架构编写，如有更新请参考最新代码实现。