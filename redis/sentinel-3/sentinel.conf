sentinel monitor mymaster *********** 6379 2
sentinel resolve-hostnames yes
sentinel announce-hostnames yes
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 60000

# Generated by CONFIG REWRITE
port 26379
dir "/data"
latency-tracking-info-percentiles 50 99 99.9
user default on nopass sanitize-payload ~* &* +@all
sentinel myid 8bfe5a8bf4554291d67f89fd6325d9d4c26d557d
sentinel config-epoch mymaster 0
sentinel leader-epoch mymaster 1
sentinel current-epoch 1

sentinel known-replica mymaster redis-slave-1 6379

sentinel known-replica mymaster redis-slave-2 6379

sentinel known-sentinel mymaster *********** 26379 a99f0377dcea750405c044a444b5528f10105bd7

sentinel known-sentinel mymaster *********** 26379 6989c4cbc6732a22ac9c1bbc4771cdc362a89a4c

